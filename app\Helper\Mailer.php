<?php

namespace App\Helper;

use App\Mail\TestReply;
use App\Models\Tenant\EmailApi;
use App\Models\Tenant\EmailApiBody;
use App\Models\Tenant\EmailApiHeader;
use App\Models\Tenant\EmailSmtp;
use Illuminate\Support\Facades\Facade;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;

class Mailer extends Facade
{

    public static function send($to, $subject, $content, $cc = null, $bcc = null, $attachment = [])
    {
        $methods= null;
        $smtpCounter = EmailSmtp::whereStatus(true)->count();
        $apiCounter  = EmailApi::count();

        if ($smtpCounter > 0)
        {
            $methods = "smpt";
        }
        elseif ($apiCounter > 0)
        {
            $methods = "api";
        }
        else
        {
            $methods = "default";
        }


        try {
            if ($methods == "smtp") {
                $mail_app = EmailSmtp::whereStatus(true)->first();
                $config = config('mail.mailers.smtp');

                $configFrom = config('mail.from');
                $configFrom['address'] = $mail_app->smtp_mail_username;
                $configFrom['name'] = $mail_app->smtp_mail_from_name;

                $config['host'] = $mail_app->smtp_mail_host;
                $config['port'] = $mail_app->smtp_mail_port;
                $config['username'] = $mail_app->smtp_mail_username;
                $config['password'] = $mail_app->smtp_mail_password;
                $config['encryption'] = $mail_app->smtp_mail_encryption;

                config(['mail.mailers.smtp' => $config]);
                config(['mail.from' => $configFrom]);

                $string = $to;
                $delimiter = ",";

                $result = explode($delimiter, $string);
                $result2 = explode($delimiter, $cc);
                $result3 = explode($delimiter, $bcc);

                $runMessage = $content;

                if ($cc != '') {
                    if ($bcc != '') {
                        $mail = Mail::to($result)->cc($result2)->bcc($result3)->send(new TestReply($subject, $runMessage, $attachment));
                    } else {
                        $mail = Mail::to($result)->cc($result2)->send(new TestReply($subject, $runMessage, $attachment));
                    }
                } elseif ($bcc != '') {
                    $mail = Mail::to($result)->bcc($result3)->send(new TestReply($subject, $runMessage, $attachment));
                } else {
                    $mail = Mail::to($result)->send(new TestReply($subject, $runMessage, $attachment));
                }

                return $mail;

            }
            elseif ($methods == "api") {
                $api = EmailApi::first();
                $headersDB = EmailApiHeader::get();
                $bodiesDB = EmailApiBody::get();

                $url = $api->url;
                $method = $api->method;
                $headers = [];
                $bodies = [];

                foreach ($headersDB as $header) {
                    $headers[$header->key] = $header->value;
                }
                foreach ($bodiesDB as $body) {
                    switch ($body->variable) {
                        case "to":
                            $bodies[$body->key] = $to;
                            break;
                        case "cc":
                            $cc !== null ? $bodies[$body->key] = $cc : null;
                            break;
                        case "bcc":
                            $bcc !== null ? $bodies[$body->key] = $bcc : null;
                            break;
                        case "subject":
                            $bodies[$body->key] = $subject;
                            break;
                        case "body":
                            $bodies[$body->key] = $content;
                            break;
                        default:
                            $bodies[$body->key] = [$body->value];
                    }
                }

                $response = "";
                if ($method == 'GET') {
                    $response = Http::withHeaders($headers)->get($url, $bodies);
                } elseif ($method == 'POST') {
                    $response = Http::withHeaders($headers)->post($url, $bodies);
                }

                return $response;

            }
            else {
                $string = $to;
                $delimiter = ",";

                $result = explode($delimiter, $string);
                $result2 = explode($delimiter, $cc);
                $result3 = explode($delimiter, $bcc);

                $runMessage = $content;

                if ($cc != '') {
                    if ($bcc != '') {
                        $mail = Mail::to($result)->cc($result2)->bcc($result3)->send(new TestReply($subject, $runMessage, $attachment));
                    } else {
                        $mail = Mail::to($result)->cc($result2)->send(new TestReply($subject, $runMessage, $attachment));
                    }
                } elseif ($bcc != '') {
                    $mail = Mail::to($result)->bcc($result3)->send(new TestReply($subject, $runMessage, $attachment));
                } else {
                    $mail = Mail::to($result)->send(new TestReply($subject, $runMessage, $attachment));
                }

                return $mail;
            }

        } catch (\Exception $e) {
            // Handle the exception
            // You can log the error, return a custom error message, etc.
            return true;
        }

    }
}
