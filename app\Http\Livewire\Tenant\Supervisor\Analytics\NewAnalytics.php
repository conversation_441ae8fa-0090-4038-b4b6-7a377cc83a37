<?php

namespace App\Http\Livewire\Tenant\Supervisor\Analytics;

use App\Models\Tenant\Resource;
use App\Models\Tenant\Ticket;
use Carbon\Carbon;
use DateTime;
use Illuminate\Support\Str;
use Livewire\Component;
use Illuminate\Support\Facades\Log;

class NewAnalytics extends Component
{
    // Filter Properties
    public $selectedDateFilter = '30 Days';
    public $selectedChannel = '';
    public $fromDate = '';
    public $toDate = '';
    public $showFilterPopup = false;

    // Filter Methods
    public function updatedSelectedDateFilter()
    {
        // Only reset custom dates if a predefined filter is selected
        if ($this->selectedDateFilter && $this->selectedDateFilter !== '') {
            $this->resetCustomDates();
        }
        $this->refreshData();
    }

    public function updatedSelectedChannel()
    {
        // If Voice is selected, clear any social media channel selection
        if ($this->selectedChannel === 'Voice') {
            // Voice tickets don't use social media channels
            Log::info('Voice channel selected, clearing social media filters');
        } elseif ($this->selectedChannel === 'Non-Voice') {
            // Non-Voice selected but no specific channel
            Log::info('Non-Voice channel selected');
        } elseif (
            !empty($this->selectedChannel) &&
            !in_array($this->selectedChannel, ['Voice', 'Non-Voice'])
        ) {
            // Specific social media channel selected
            Log::info('Specific social media channel selected: ' . $this->selectedChannel);
        }

        $this->refreshData();
    }

    public function updatedFromDate()
    {
        // Clear predefined date filter when custom dates are used
        if ($this->fromDate && $this->toDate) {
            $this->selectedDateFilter = '';
        }
        if ($this->fromDate && $this->toDate) {
            $this->refreshData();
        }
    }

    public function updatedToDate()
    {
        // Clear predefined date filter when custom dates are used
        if ($this->fromDate && $this->toDate) {
            $this->selectedDateFilter = '';
        }
        if ($this->fromDate && $this->toDate) {
            $this->refreshData();
        }
    }

    public function toggleFilterPopup()
    {
        $this->showFilterPopup = !$this->showFilterPopup;
    }

    public function resetFilters()
    {
        $this->selectedDateFilter = '30 Days';
        $this->selectedChannel = '';
        $this->fromDate = '';
        $this->toDate = '';
        $this->showFilterPopup = false;
        $this->refreshData();
    }

    private function resetCustomDates()
    {
        $this->fromDate = '';
        $this->toDate = '';
    }

    public function applyFilters()
    {
        $this->showFilterPopup = false;
        $this->refreshData();
    }

    private function refreshData()
    {
        // Emit event to refresh chart with new data
        $this->emit('refreshReceivedTrendChart', $this->getReceivedTrendData());
    }

    // Received Trend Data with improved filtering logic
    public function getReceivedTrendData()
    {
        // Determine date range
        $dateRange = $this->getDateRange();
        $fromDate = $dateRange['from'];
        $toDate = $dateRange['to'];

        // Handle Voice vs Non-Voice filtering
        if ($this->selectedChannel === 'Voice') {
            // For Voice: Use Ticket model
            $data = Ticket::where('created_at', '>=', $fromDate)
                ->where('created_at', '<=', $toDate)
                ->get();
        } elseif ($this->selectedChannel === 'Non-Voice') {
            // For Non-Voice: Use ResourceAction with 'New' action
            $data = \App\Models\Tenant\ResourceAction::join('resources', 'resource_actions.resource_id', '=', 'resources.id')
                ->join('resource_infos', 'resources.id', '=', 'resource_infos.resource_id')
                ->where('resource_actions.action', 'New')
                ->where('resource_actions.created_at', '>=', $fromDate)
                ->where('resource_actions.created_at', '<=', $toDate)
                ->get();
        } elseif (!empty($this->selectedChannel) && $this->selectedChannel !== 'Voice' && $this->selectedChannel !== 'Non-Voice') {
            // For specific social media channels
            $data = \App\Models\Tenant\ResourceAction::join('resources', 'resource_actions.resource_id', '=', 'resources.id')
                ->join('resource_infos', 'resources.id', '=', 'resource_infos.resource_id')
                ->where('resource_actions.action', 'New')
                ->where('resource_infos.channel', '=', $this->selectedChannel)
                ->where('resource_actions.created_at', '>=', $fromDate)
                ->where('resource_actions.created_at', '<=', $toDate)
                ->get();
        } else {
            // No filter: Get all ResourceActions with 'New' action (Non-Voice by default)
            $data = \App\Models\Tenant\ResourceAction::where('action', 'New')
                ->where('created_at', '>=', $fromDate)
                ->where('created_at', '<=', $toDate)
                ->get();
        }

        // Debug: Log query details
        Log::info('Query details:', [
            'selectedChannel' => $this->selectedChannel,
            'fromDate' => $fromDate->format('Y-m-d H:i:s'),
            'toDate' => $toDate->format('Y-m-d H:i:s'),
            'recordCount' => $data->count(),
            'customDateRange' => !empty($this->fromDate) && !empty($this->toDate)
        ]);

        // Initialize counters
        $rec_t_sat = 0;
        $rec_t_sun = 0;
        $rec_t_mon = 0;
        $rec_t_tue = 0;
        $rec_t_wed = 0;
        $rec_t_thu = 0;
        $rec_t_fri = 0;

        // Count by day of week
        foreach ($data as $dat) {
            $dayName = $dat->created_at->format('l');
            switch ($dayName) {
                case 'Saturday':
                    $rec_t_sat++;
                    break;
                case 'Sunday':
                    $rec_t_sun++;
                    break;
                case 'Monday':
                    $rec_t_mon++;
                    break;
                case 'Tuesday':
                    $rec_t_tue++;
                    break;
                case 'Wednesday':
                    $rec_t_wed++;
                    break;
                case 'Thursday':
                    $rec_t_thu++;
                    break;
                case 'Friday':
                    $rec_t_fri++;
                    break;
            }
        }

        return [
            'sat' => $rec_t_sat,
            'sun' => $rec_t_sun,
            'mon' => $rec_t_mon,
            'tue' => $rec_t_tue,
            'wed' => $rec_t_wed,
            'thu' => $rec_t_thu,
            'fri' => $rec_t_fri,
        ];
    }

    private function getDateRange()
    {
        // If custom date range is provided, use it
        if (!empty($this->fromDate) && !empty($this->toDate)) {
            return [
                'from' => Carbon::parse($this->fromDate)->startOfDay(),
                'to' => Carbon::parse($this->toDate)->endOfDay()
            ];
        }

        // Otherwise, use predefined date filter
        switch ($this->selectedDateFilter) {
            case '24 Hours':
                return [
                    'from' => now()->subHours(24),
                    'to' => now()
                ];
            case '7 Days':
                return [
                    'from' => now()->subDays(7),
                    'to' => now()
                ];
            case '30 Days':
                return [
                    'from' => now()->subDays(30),
                    'to' => now()
                ];
            case '60 Days':
                return [
                    'from' => now()->subDays(60),
                    'to' => now()
                ];
            default:
                return [
                    'from' => now()->subDays(30),
                    'to' => now()
                ];
        }
    }

    public function mount()
    {
        // Initialize with default values
        $this->selectedDateFilter = '30 Days';
        $this->selectedChannel = '';

        // Get initial data and emit to chart
        $data = $this->getReceivedTrendData();

        // Debug: Log the data to see what we're getting
        Log::info('Initial Received Trend Data:', $data);

        // Emit initial data to the chart when component mounts
        $this->emit('refreshReceivedTrendChart', $data);
    }

    public function hydrate()
    {
        // This runs after every Livewire request
        // Ensure data is refreshed after any property changes
        $this->emit('refreshReceivedTrendChart', $this->getReceivedTrendData());
    }

    public function render()
    {
        return view('livewire.tenant.supervisor.analytics.new-analytics');
    }
}
