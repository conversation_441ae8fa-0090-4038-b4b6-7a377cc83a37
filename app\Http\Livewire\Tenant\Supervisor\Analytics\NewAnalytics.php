<?php

namespace App\Http\Livewire\Tenant\Supervisor\Analytics;

use App\Models\Tenant\Resource;
use App\Models\Tenant\Ticket;
use Carbon\Carbon;
use DateTime;
use Illuminate\Support\Str;
use Livewire\Component;
use Illuminate\Support\Facades\Log;

class NewAnalytics extends Component
{
    // Filter Properties - Exact same as old analytics
    public $selectedDateFilter = '30 Days';
    public $selectedChannel = '';
    public $fromDate = '';
    public $toDate = '';
    public $showFilterPopup = false;

    // Filter Methods - Exact same functionality as old analytics
    public function updatedSelectedDateFilter()
    {
        $this->resetCustomDates();
        // Refresh chart when date filter changes
        $this->emit('refreshReceivedTrendChart', $this->getReceivedTrendData());
    }

    public function updatedSelectedChannel()
    {
        // Refresh chart when channel filter changes
        $this->emit('refreshReceivedTrendChart', $this->getReceivedTrendData());
    }

    public function updatedFromDate()
    {
        if ($this->fromDate && $this->toDate) {
            $this->selectedDateFilter = '';
        }
    }

    public function updatedToDate()
    {
        if ($this->fromDate && $this->toDate) {
            $this->selectedDateFilter = '';
        }
    }

    public function toggleFilterPopup()
    {
        $this->showFilterPopup = !$this->showFilterPopup;
    }

    public function resetFilters()
    {
        $this->selectedDateFilter = '30 Days';
        $this->selectedChannel = '';
        $this->fromDate = '';
        $this->toDate = '';
        $this->showFilterPopup = false;
        // Refresh chart when filters are reset
        $this->emit('refreshReceivedTrendChart', $this->getReceivedTrendData());
    }

    private function resetCustomDates()
    {
        $this->fromDate = '';
        $this->toDate = '';
    }

    public function applyFilters()
    {
        $this->showFilterPopup = false;
        // Emit event to refresh chart with new data
        $this->emit('refreshReceivedTrendChart', $this->getReceivedTrendData());
    }

    // Received Trend Data - Exact same logic as old analytics
    public function getReceivedTrendData()
    {
        // Get date filter value
        $datFilter = $this->getDateFilterDays();

        // Get ResourceAction data with 'New' action - same as old analytics
        if (!empty($this->selectedChannel)) {
            $data30 = \App\Models\Tenant\ResourceAction::join('resources', 'resource_actions.resource_id', '=', 'resources.id')
                ->join('resource_infos', 'resources.id', '=', 'resource_infos.resource_id')
                ->where('resource_actions.action', 'New')
                ->where('resource_infos.channel', '=', $this->selectedChannel)
                ->where('resource_actions.created_at', '>=', now()->subDays($datFilter))
                ->get();
        } else {
            $data30 = \App\Models\Tenant\ResourceAction::where('action', 'New')
                ->where('created_at', '>=', now()->subDays($datFilter))
                ->get();
        }

        // Debug: Log query details
        Log::info('Query details:', [
            'selectedChannel' => $this->selectedChannel,
            'datFilter' => $datFilter,
            'recordCount' => $data30->count(),
            'dateRange' => now()->subDays($datFilter)->format('Y-m-d H:i:s') . ' to ' . now()->format('Y-m-d H:i:s')
        ]);

        // Initialize counters - same as old analytics
        $rec_t_sat = 0;
        $rec_t_sun = 0;
        $rec_t_mon = 0;
        $rec_t_tue = 0;
        $rec_t_wed = 0;
        $rec_t_thu = 0;
        $rec_t_fri = 0;

        // Count by day of week - exact same logic as old analytics
        foreach ($data30 as $dat) {
            $dayName = $dat->created_at->format('l');
            switch ($dayName) {
                case 'Saturday':
                    $rec_t_sat++;
                    break;
                case 'Sunday':
                    $rec_t_sun++;
                    break;
                case 'Monday':
                    $rec_t_mon++;
                    break;
                case 'Tuesday':
                    $rec_t_tue++;
                    break;
                case 'Wednesday':
                    $rec_t_wed++;
                    break;
                case 'Thursday':
                    $rec_t_thu++;
                    break;
                case 'Friday':
                    $rec_t_fri++;
                    break;
            }
        }

        return [
            'sat' => $rec_t_sat,
            'sun' => $rec_t_sun,
            'mon' => $rec_t_mon,
            'tue' => $rec_t_tue,
            'wed' => $rec_t_wed,
            'thu' => $rec_t_thu,
            'fri' => $rec_t_fri,
        ];
    }

    private function getDateFilterDays()
    {
        switch ($this->selectedDateFilter) {
            case '24 Hours':
                return 1;
            case '7 Days':
                return 7;
            case '30 Days':
                return 30;
            case '60 Days':
                return 60;
            default:
                return 30;
        }
    }

    public function mount()
    {
        // Get initial data and emit to chart
        $data = $this->getReceivedTrendData();

        // Debug: Log the data to see what we're getting
        Log::info('Received Trend Data:', $data);

        // Emit initial data to the chart when component mounts
        $this->emit('refreshReceivedTrendChart', $data);
    }

    public function render()
    {
        return view('livewire.tenant.supervisor.analytics.new-analytics');
    }
}
