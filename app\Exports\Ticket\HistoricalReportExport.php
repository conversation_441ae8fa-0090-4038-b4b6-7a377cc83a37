<?php

namespace App\Exports\Ticket;

use App\Models\Tenant\Ticket;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Illuminate\Contracts\Queue\ShouldQueue as LaravelShouldQueue;

class HistoricalReportExport implements FromQuery, WithHeadings, WithMapping, LaravelShouldQueue
{
    protected $levels, $from, $to, $lastKey, $maxIds, $category_array;

    public function __construct($levels, $from, $to, $lastKey, $maxIds, $category_array)
    {
        $this->levels = $levels;
        $this->from = $from;
        $this->to = $to;
        $this->lastKey = $lastKey;
        $this->maxIds = $maxIds;
        $this->category_array = $category_array;
    }

    public function query()
    {
        return Ticket::select('id')
            ->with([
                'ticketCustomer:id,ticket_id,display_name,mobile_number',
                'ticketInfo:id,ticket_id,channel,type,priority,country,language,created_at',
                'ticketActions:id,ticket_id,action,created_at,user_id,department_id,comment',
                'ticketActions.user:id,first_name,last_name',
                'ticketActions.department:id,name',
                'ticketCategories:id,ticket_id,category_id',
                'ticketCategories.category:id,name'
            ])
            
            ->whereBetween('created_at', [$this->from . " 00:00:00", $this->to . " 23:59:59"])
            ->when($this->lastKey !== null, function ($query) {
                $query->whereHas('ticketCategories', function ($q) {
                    $q->where('category_id', $this->category_array[$this->lastKey])
                      ->whereIn('id', $this->maxIds);
                });
            });
    }

    public function headings(): array
    {
        return array_merge([
            'Ticket ID', 'Channel', 'Type', 'Customer Name', 'Customer Number',
            'Status', 'Department', 'Priority', 'Country', 'Language'
        ], $this->levels->pluck('label')->toArray(), ['User', 'Date', 'Comment']);
    }

    public function map($ticket): array
    {
        $mappedData = [];

        foreach ($ticket->ticketActions as $action) {
            $row = [
                $ticket->id,
                $ticket->ticketInfo->channel ?? '-',
                $ticket->ticketInfo->type ?? '-',
                $ticket->ticketCustomer->display_name ?? '-',
                $ticket->ticketCustomer->mobile_number ?? '-',
                $action->action === "New Ticket" ? "New" : $action->action,
                $action->department->name ?? '-',
                $ticket->ticketInfo->priority ?? '-',
                $ticket->ticketInfo->country ?? '-',
                $ticket->ticketInfo->language ?? '-',
            ];

            $categories = $ticket->ticketCategories->take(count($this->levels));

            if ($categories->isNotEmpty()) {
                foreach ($categories as $cat) {
                    $row[] = optional($cat->category)->name ?? '-';
                }
            } else {
                foreach ($this->levels->pluck('label')->toArray() as $level) {
                    $row[] = '-'; // Placeholder for missing categories
                }
            }

            $row[] = ($action->user->first_name ?? '-') . " " . ($action->user->last_name ?? '-');
            $row[] = $action->created_at->format('Y-m-d H:i:s');
            $row[] = $action->comment ?? '-';

            $mappedData[] = $row;
        }

        return $mappedData;
    }
}
