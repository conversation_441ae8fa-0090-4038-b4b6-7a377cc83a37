<?php

namespace App\Events\Webhook\Email;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class EmailNotify implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $tenant_id;
    public $email;

    public function __construct($tenant_id, $email)
    {
        $this->tenant_id    =   $tenant_id;
        $this->email      =   $email;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastWith()
    {
        return [
            'tenant_id'     =>$this->tenant_id,
            'content'       =>$this->email,

        ];
    }

    public function broadcastOn()
    {
        return new PrivateChannel('Email.'.$this->tenant_id);
    }
}
