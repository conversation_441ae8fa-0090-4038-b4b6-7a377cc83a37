<?php

namespace App\Events\Ticket;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class SystemNotify implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $tenant_id;
    public $department_id;
    public $agent;
    public $message;
    public $url;
    public $ticket;
    public $ticketInfo;

    public function __construct($tenant_id, $department_id,$agent,$message,$url,$ticket,$ticketInfo)
    {
        $this->tenant_id        =   $tenant_id;
        $this->department_id    =   $department_id;
        $this->agent            =   $agent;
        $this->message          =   $message;
        $this->url          =   $url;
        $this->ticket          =   $ticket;
        $this->ticketInfo          =   $ticketInfo;
    }

    public function broadcastWith()
    {
        return [
            'tenant_id'       => $this->tenant_id,
            'department_id'   => $this->department_id,
            'agent'           => $this->agent,
            'message'         => $this->message,
            'url'         => $this->url,
            'ticket'         => $this->ticket,
            '$this'         => $this->ticketInfo,

        ];
    }
    public function broadcastOn()
    {
        return new PrivateChannel('NewTicket.'.$this->tenant_id);
    }
}
