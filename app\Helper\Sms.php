<?php

namespace App\Helper;

use App\Models\Tenant\SmsApi;
use App\Models\Tenant\SmsApiBody;
use App\Models\Tenant\SmsApiHeader;
use Illuminate\Support\Facades\Facade;
use Illuminate\Support\Facades\Http;

class Sms extends Facade
{
    public static function send($to , $content)
    {
        $methods= null;
        $smsCounter = SmsApi::whereStatus(true)->count();


        if ($smsCounter > 0)
        {
            $methods = "api";
        }

        if ($methods == "api")
        {
            $api            = SmsApi::first();
            $headersDB      = SmsApiHeader::get();
            $bodiesDB       = SmsApiBody::get();

            $url        = $api->url;
            $method     = $api->method;
            $headers    = [];
            $bodies     = [];

            foreach ($headersDB as $header) {
                $headers[$header->key] = $header->value;
            }
            $url .="?";
            foreach ($bodiesDB as $body) {
                switch ($body->variable) {
                    case "from":
                        $bodies[$body->key]  = $to;
                        $url .=$body->key."=".$to . "&";
                        break;
                    case "body":
                        $bodies[$body->key] = $content;
                        $url .=$body->key."=".$content. "&";
                        break;
                    default:
                        $bodies[$body->key] = [$body->value];
                        $url .=$body->key."=".$body->value. "&";
                }


            }

            $response ="";

            // Build query string for GET request
//            $queryParams = http_build_query($bodies);

//            dd($url);

            if ($method == 'GET') {
                $response = Http::withHeaders($headers)->get($url);
            } elseif ($method == 'POST') {
                $response = Http::withHeaders($headers)->post($url);
            }

            return $response;

        }

    }
}
