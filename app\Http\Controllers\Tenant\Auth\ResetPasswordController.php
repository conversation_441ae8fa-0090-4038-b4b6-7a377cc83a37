<?php

namespace App\Http\Controllers\Tenant\Auth;

use App\Models\Tenant\User;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Hash;
use App\Models\Tenant\PasswordHistory;
use App\Providers\RouteServiceProvider;
use Illuminate\Foundation\Auth\ResetsPasswords;

class ResetPasswordController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Password Reset Controller
    |--------------------------------------------------------------------------
    |
    | This controller is responsible for handling password reset requests
    | and uses a simple trait to include this behavior. You're free to
    | explore this trait and override any methods you wish to tweak.
    |
    */

    use ResetsPasswords;

    /**
     * Where to redirect users after resetting their password.
     *
     * @var string
     */
    protected $redirectTo = RouteServiceProvider::HOME;

    public function showResetForm(Request $request)
    {

        $token = $request->route()->parameter('token');

        return view('tenant.auth.passwords.reset')->with(
            ['token' => $token, 'email' => $request->email]
        );
    }

    protected function rules()
    {
        return [
            'token' => 'required',
            'email' => 'required|email',
            'password' => ['required', 'min:8', 'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*(_|[^\w])).+$/', 'confirmed'],
        ];
    }


    public function reset(Request $request)
    {
        $request->validate($this->rules(), $this->validationErrorMessages());



        $user = User::where('email', $request->email)->first();

        // Check if the new password matches any of the last 5 passwords in password history
        $recentPasswords = PasswordHistory::where('user_id', $user->id)
            ->latest()
            ->take(5)
            ->pluck('password');


        foreach ($recentPasswords as $oldPassword) {
            if (Hash::check($request->password, $oldPassword)) {
                return redirect()->back()->withErrors(['password' => 'You cannot reuse any of your last 5 passwords.']);
            }
        }

        // Check if the new password matches the current password
        if (Hash::check($request->password, $user->password)) {

            return redirect()->back()->withErrors(['password' => 'Your new password cannot be the same as your current password.']);
        }

        // make a copy of the current password into the password histories table 
        PasswordHistory::create([
            'user_id' => $user->id,
            'password' => $user->password,
        ]);
        
        // Update the user password, password last reset time, and remember token (reset every 3 months part)
        $user->password = Hash::make($request->password);
        $user->password_last_reset_at = now();
        $user->setRememberToken(Str::random(60));
        $user->save();

        // PasswordHistory::create([
        //     'user_id' => $user->id,
        //     'password' => $user->password,
        // ]);

        // Delete oldest records if there are more than 5
        if (PasswordHistory::where('user_id', $user->id)->count() > 5) {
            PasswordHistory::where('user_id', $user->id)
                ->oldest()
                ->take(1)
                ->delete();
        }

        // Log the user in after successful password reset
        $this->guard()->login($user);

        // Here we will attempt to reset the user's password. If it is successful we
        // will update the password on an actual user model and persist it to the
        // database. Otherwise we will parse the error and return the response.
        $response = $this->broker()->reset(
            $this->credentials($request),
            function ($user, $password) {
                $this->resetPassword($user, $password);
            }
        );

        // If the password was successfully reset, we will redirect the user back to
        // the application's home authenticated view. If there is an error we can
        // redirect them back to where they came from with their error message.
        return $response == Password::PASSWORD_RESET
            ? $this->sendResetResponse($request, $response)
            : $this->sendResetFailedResponse($request, $response);
    }
}
