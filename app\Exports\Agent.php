<?php

namespace App\Exports;

use App\Models\Tenant\Role;
use App\Models\Tenant\User;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class Agent implements FromCollection, WithHeadings
{
    use Exportable;
    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return Role::find(4)->User()->select('id', 'first_name', 'last_name')->get();
    }

    public function headings(): array
    {
        return ['id', 'First_Name', 'Last_Name'];
    }
}
