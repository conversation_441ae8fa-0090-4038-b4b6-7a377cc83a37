<?php

namespace App\Http\Controllers\Tenant\Supervisor\Analytics;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class NewAnalyticsController extends Controller
{
    public function __invoke()
    {
        if (!auth()->user()->can(['analytics'])) {
            return redirect()->route('supervisor.no-permission');
        }

        return view('tenant.supervisor.analytics.new-analytics');
    }
}
