<?php

namespace App\Http\Controllers\Tenant\Client\Analystics;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Resource;
use App\Models\Tenant\ResourceAction;
use App\Models\Tenant\ResourceInfo;
use App\Models\Tenant\Ticket;
use App\Models\Tenant\TicketAction;
use App\Models\Tenant\User;
use App\Models\User as ModelsUser;
use Carbon\Carbon;
use DateTime;
use Illuminate\Http\Request;

use function Sodium\compare;

class AnalysticsController extends Controller
{
    public $updatedData;


    public function __invoke()
    {

        $unresolv = \App\Models\Tenant\Ticket::where('status','New')->count();

        $received_tickets = Ticket::where('status','New')->count();
        $received_tickets_sat = 0;
        $received_tickets_sun = 0;
        $received_tickets_mon = 0;
        $received_tickets_tue = 0;
        $received_tickets_wed = 0;
        $received_tickets_thu = 0;
        $received_tickets_fri = 0;
        $received_tickets_total = 0;


        $rec_t_sat = 0;
        $rec_t_sun = 0;
        $rec_t_mon = 0;
        $rec_t_tue = 0;
        $rec_t_wed = 0;
        $rec_t_thu = 0;
        $rec_t_fri = 0;

        $closed_tickets_sat = 0;
        $closed_tickets_sun = 0;
        $closed_tickets_mon = 0;
        $closed_tickets_tue = 0;
        $closed_tickets_wed = 0;
        $closed_tickets_thu = 0;
        $closed_tickets_fri = 0;
        $closed_tickets_total = 0;

        $open_tickets_sat = 0;
        $open_tickets_sun = 0;
        $open_tickets_mon = 0;
        $open_tickets_tue = 0;
        $open_tickets_wed = 0;
        $open_tickets_thu = 0;
        $open_tickets_fri = 0;
        $open_tickets_total = 0;
        $voice_tickets = Ticket::count();
        $resolved_tickets = Ticket::whereStatus('Resolved')->count();
        $unresolved_tickets = Ticket::where('status','!=','Resolved')->count();
        $resolved_tickets2 = 0;
        $unresolved_tickets2 = 0;
        $sm_tickets = 0;
        $chat_tickets = $chat_tickets = ResourceInfo::where('channel','!=','Facebook')->where('channel','!=','Instagram')->where('channel','!=','Email')->where('channel','!=','Twitter')->where('channel','!=','Google')->count();
        $New_Ticket_time = '';
        $Resolved_time = '';
        $useridd = '';

        $sm_tickets = ResourceInfo::where('channel','!=','Whatsapp')->where('channel','!=','Livechat')->count();


        $data2 = TicketAction::all();

//        foreach ($data2 as $dat2) {
//
//            if ($dat2->action == "New Ticket") {
//                $unresolved_tickets++;
//            }
//
//            if ($dat2->action == "Resolved") {
//                $resolved_tickets++;
////                $unresolved_tickets--;
//            }
//        }

        $data36 = TicketAction::all();

        foreach ($data36 as $dat36) {

            if ($dat36->action == "New_Ticket") {
                $userid = $dat36->user_id;
                $userss = User::where("id", "=", $userid)->get();

                foreach ($userss as $us) {
                    $useridd = $us->username;
                }

                $unresolved_tickets2++;
            }
            if ($dat36->action == "Resolved") {
                $resolved_tickets2++;
                $unresolved_tickets2--;
            }
        }

        try {
            $all_agents = User::query()->with('agentRoles')->whereHas('roles', function ($q) {
                $q->where('allowed_route', 'agent');
            });

            foreach ($all_agents->get() as $agent) {
                $username = $agent->username;
            }

            $data411 = ResourceAction::where('action', '=', 'New_Ticket')
                ->where('user_id', '=', $username)
                ->first();

            if ($data411 !== null) {
                // Access the properties of the record
                $New_Ticket_time2 = $data411->updated_at;
            } else {
                $New_Ticket_time2 = "";

                // Handle the case when no matching records are found
            }

            $data5 = ResourceAction::where('action', '=', 'Resolved')
                ->where('user_id', '=', $username)
                ->latest()
                ->get();

            if ($data5->isNotEmpty()) {
                // Access the properties of the first record or iterate over the collection
                $Resolved_time2 = $data5->first()->updated_at;
            } else {

                $Resolved_time2 = "";

                // Handle the case when no matching records are found
            }

            if ($Resolved_time2 != "" and $New_Ticket_time2 != "") {


                $startDate2 = Carbon::parse($New_Ticket_time2);
                $endDate2 = Carbon::parse($Resolved_time2);

                $diffInHours2 = $startDate2->diffInHours($endDate2);
                $diffInMinutes2 = $startDate2->diffInMinutes($endDate2);
                $diffInSec2 = $startDate2->diffInSeconds($endDate2);

                $remainingSec2 = $diffInSec2 % 60;
                $totalMinutes2 = $diffInMinutes2 + floor($diffInSec2 / 60);

                if ($totalMinutes2 >= 60) {
                    $extraHours2 = floor($totalMinutes2 / 60);
                    $totalMinutes2 = $totalMinutes2 % 60;
                    $diffInHours2 += $extraHours2;
                }

                $output2 = sprintf("%02d:%02d:%02d", $diffInHours2, $totalMinutes2, $remainingSec2);
            } else {

                $output2 = "00:00:00";
            }
            // dd($all_agents);
        } catch (\Exception $e) {
            // $this->alert('error', 'Something was wrong !', [
            //     'timerProgressBar' => true,
            // 'showCloseButton' => true,
            //     'timer' => '6000',
            // ]);
        }
        $data3 = ResourceAction::where('action', '=', 'New_Ticket')->orWhere('action', '=', 'Resolved')->get();

        foreach ($data3 as $dat3) {

            $date_day = Carbon::parse($dat3->created_at);
            $dayName = $date_day->format('l');

            if ($dayName == "Saturday") {
                $received_tickets_sat++;
                $received_tickets_total++;
            } elseif ($dayName == "Sunday") {
                $received_tickets_sun++;
                $received_tickets_total++;
            } elseif ($dayName == "Monday") {
                $received_tickets_mon++;
                $received_tickets_total++;
            } elseif ($dayName == "Tuesday") {
                $received_tickets_tue++;
                $received_tickets_total++;
            } elseif ($dayName == "Wednesday") {
                $received_tickets_wed++;
                $received_tickets_total++;
            } elseif ($dayName == "Thursday") {
                $received_tickets_thu++;
                $received_tickets_total++;
            } elseif ($dayName == "Friday") {
                $received_tickets_fri++;
                $received_tickets_total++;
            }
        }

        $data4 = ResourceAction::where('action', '=', 'New_Ticket')
            ->first();

        if ($data4 !== null) {
            // Access the properties of the record
            $New_Ticket_time = $data4->updated_at;
        } else {
            // Handle the case when no matching records are found
        }

        $data5 = ResourceAction::where('action', '=', 'Resolved')
            ->latest()
            ->get();

        if ($data5->isNotEmpty()) {
            // Access the properties of the first record or iterate over the collection
            $Resolved_time = $data5->first()->updated_at;
        } else {
            // Handle the case when no matching records are found
        }

        $data6 = ResourceAction::where('action', '=', 'New_Ticket')->orWhere('action', '=', 'Closed')->get();

        foreach ($data6 as $dat6) {
            if ($dat6->action == "New_Ticket") {
                $date_day2 = Carbon::parse($dat6->created_at);
                $dayName2 = $date_day2->format('l');

                if ($dayName2 == "Saturday") {
                    $open_tickets_sat++;
                    $open_tickets_total++;
                } elseif ($dayName2 == "Sunday") {
                    $open_tickets_sun++;
                    $open_tickets_total++;
                } elseif ($dayName2 == "Monday") {
                    $open_tickets_mon++;
                    $open_tickets_total++;
                } elseif ($dayName2 == "Tuesday") {
                    $open_tickets_tue++;
                    $open_tickets_total++;
                } elseif ($dayName2 == "Wednesday") {
                    $open_tickets_wed++;
                    $open_tickets_total++;
                } elseif ($dayName2 == "Thursday") {
                    $open_tickets_thu++;
                    $open_tickets_total++;
                } elseif ($dayName2 == "Friday") {
                    $open_tickets_fri++;
                    $open_tickets_total++;
                }
            } else {
                $date_day3 = Carbon::parse($dat6->created_at);
                $dayName3 = $date_day3->format('l');
                if ($dayName3 == "Saturday") {
                    $closed_tickets_sat++;
                    $closed_tickets_total++;
                } elseif ($dayName3 == "Sunday") {
                    $closed_tickets_sun++;
                    $closed_tickets_total++;
                } elseif ($dayName3 == "Monday") {
                    $closed_tickets_mon++;
                    $closed_tickets_total++;
                } elseif ($dayName3 == "Tuesday") {
                    $closed_tickets_tue++;
                    $closed_tickets_total++;
                } elseif ($dayName3 == "Wednesday") {
                    $closed_tickets_wed++;
                    $closed_tickets_total++;
                } elseif ($dayName3 == "Thursday") {
                    $closed_tickets_thu++;
                    $closed_tickets_total++;
                } elseif ($dayName3 == "Friday") {
                    $closed_tickets_fri++;
                    $closed_tickets_total++;
                }
            }
        }


        $data30 = ResourceAction::where('action', 'New')->where('created_at', '>=', now()->subDays(30))->get();

        $rec_t_sat = 0;
        $rec_t_sun = 0;
        $rec_t_mon = 0;
        $rec_t_tue = 0;
        $rec_t_wed = 0;
        $rec_t_thu = 0;
        $rec_t_fri = 0;

        foreach ($data30 as $dat) {
            $dayName = $dat->created_at->format('l');
            switch ($dayName) {
                case 'Saturday':
                    $rec_t_sat++;
                    break;
                case 'Sunday':
                    $rec_t_sun++;
                    break;
                case 'Monday':
                    $rec_t_mon++;
                    break;
                case 'Tuesday':
                    $rec_t_tue++;
                    break;
                case 'Wednesday':
                    $rec_t_wed++;
                    break;
                case 'Thursday':
                    $rec_t_thu++;
                    break;
                case 'Friday':
                    $rec_t_fri++;
                    break;
            }
        }




        $startDate = Carbon::parse($New_Ticket_time);
        $endDate = Carbon::parse($Resolved_time);

        // $diffInDays = $startDate->diffInDays($endDate);
        $diffInHours = $startDate->diffInHours($endDate);
        $diffInMinutes = $startDate->diffInMinutes($endDate);
        $diffInSec = $startDate->diffInSeconds($endDate);



        $remainingSec = $diffInSec % 60;
        $totalMinutes = $diffInMinutes + floor($diffInSec / 60) + floor($remainingSec / 60);

        if ($remainingSec > 0 && $remainingSec <= 59) {
            $output = $totalMinutes . "m," . $remainingSec . "s";
        } else {
            $output = $totalMinutes . "m";
        }

        //for Agent
        $data31 = ResourceAction::where('action', 'New')->where('created_at', '>=', now()->subDays(30))->get();

        $rec_t_sat2 = 0;
        $rec_t_sun2 = 0;
        $rec_t_mon2 = 0;
        $rec_t_tue2 = 0;
        $rec_t_wed2 = 0;
        $rec_t_thu2 = 0;
        $rec_t_fri2 = 0;

        foreach ($data31 as $dat2) {
            $dayName2 = $dat2->created_at->format('l');
            switch ($dayName2) {
                case 'Saturday':
                    $rec_t_sat2++;
                    break;
                case 'Sunday':
                    $rec_t_sun2++;
                    break;
                case 'Monday':
                    $rec_t_mon2++;
                    break;
                case 'Tuesday':
                    $rec_t_tue2++;
                    break;
                case 'Wednesday':
                    $rec_t_wed2++;
                    break;
                case 'Thursday':
                    $rec_t_thu2++;
                    break;
                case 'Friday':
                    $rec_t_fri2++;
                    break;
            }
        }




        $startDate2 = Carbon::parse($New_Ticket_time);
        $endDate2 = Carbon::parse($Resolved_time);

        // $diffInDays = $startDate->diffInDays($endDate);
        $diffInHours = $startDate2->diffInHours($endDate2);
        $diffInMinutes = $startDate2->diffInMinutes($endDate2);
        $diffInSec = $startDate2->diffInSeconds($endDate2);



        $remainingSec = $diffInSec % 60;
        $totalMinutes = $diffInMinutes + floor($diffInSec / 60) + floor($remainingSec / 60);

        if ($remainingSec > 0 && $remainingSec <= 59) {
            $output = $totalMinutes . "m," . $remainingSec . "s";
            // $output2 = "7:25:" . $remainingSec;
        } else {
            $output = $totalMinutes . "m";
            // $output2 = $totalMinutes . ":00:00";
        }



        // echo $totalMinutes;

        return view('tenant.client.analytics.index', [
            'received_tickets' => $received_tickets,
            'voice_tickets' => $voice_tickets,
            'resolved_tickets' => $resolved_tickets,
            'unresolved_tickets' => $unresolved_tickets,
            'sm_tickets' => $sm_tickets,
            'chat_tickets' => $chat_tickets,
            'output' => $output,
            'all_agents' => $all_agents,
            'output2' => $output2,
            'diffInHours' => $diffInHours,
            'diffInMinutes' => $diffInMinutes,
            'received_tickets_sat' => $received_tickets_sat,
            'received_tickets_sun' => $received_tickets_sun,
            'received_tickets_mon' => $received_tickets_mon,
            'received_tickets_tue' => $received_tickets_tue,
            'received_tickets_wed' => $received_tickets_wed,
            'received_tickets_thu' => $received_tickets_thu,
            'received_tickets_fri' => $received_tickets_fri,
            'closed_tickets_sat' =>  $closed_tickets_sat,
            'closed_tickets_sun' =>  $closed_tickets_sun,
            'closed_tickets_mon' =>  $closed_tickets_mon,
            'closed_tickets_tue' =>  $closed_tickets_tue,
            'closed_tickets_wed' =>  $closed_tickets_wed,
            'closed_tickets_thu' =>  $closed_tickets_thu,
            'closed_tickets_fri' =>  $closed_tickets_fri,
            'open_tickets_sat' =>  $open_tickets_sat,
            'open_tickets_sun' =>  $open_tickets_sun,
            'open_tickets_mon' =>  $open_tickets_mon,
            'open_tickets_tue' =>  $open_tickets_tue,
            'open_tickets_wed' =>  $open_tickets_wed,
            'open_tickets_thu' =>  $open_tickets_thu,
            'open_tickets_fri' =>  $open_tickets_fri,
            'rec_t_sat' =>  $rec_t_sat,
            'rec_t_sun' =>  $rec_t_sun,
            'rec_t_mon' =>  $rec_t_mon,
            'rec_t_tue' =>  $rec_t_tue,
            'rec_t_wed' =>  $rec_t_wed,
            'rec_t_thu' =>  $rec_t_thu,
            'rec_t_fri' =>  $rec_t_fri,
            'data36' =>  $data36,
            'unresolved_tickets2' =>  $unresolved_tickets2,
            'resolved_tickets2' =>  $resolved_tickets2,
            'closed_tickets_total' =>   $closed_tickets_total,
            'open_tickets_total' =>   $open_tickets_total,
            'received_tickets_total' =>   $received_tickets_total,
            'useridd' =>   $useridd,
            'unresolv' =>   $unresolv,


        ], compact('data30', 'rec_t_sat', 'rec_t_sun', 'rec_t_mon', 'rec_t_tue', 'rec_t_wed', 'rec_t_thu', 'rec_t_fri'));
    }

    //HandelHere
    public function handleSelectChange(Request $request)
    {
        $mySelect = $request->input('mySelect');
        $From_date = $request->input('From_date');

        $To_date = $request->input('To_date');
        $social = $request->input('social');

        $unresolv = \App\Models\Tenant\Ticket::where('status', 'New')

            ->count();

        if ($mySelect == "") {


            if ($From_date != null && $To_date != null) {
                // Create DateTime objects from the input dates
                $fromDateTime = new DateTime($From_date);
                $toDateTime = new DateTime($To_date);

                $unresolv = Ticket::where('status', 'New')
                    ->whereBetween('created_at', [$fromDateTime, $toDateTime])
                    ->count();

                // Calculate the difference between the two dates
                $interval = $fromDateTime->diff($toDateTime);

                // Check if the difference is less than a month
                if ($interval->y < 1 && $interval->m < 1) {
                    $dat_filter = max(1, $interval->d); // Set to 1 day or the actual number of days
                } else {
                    // Build the difference string
                    $diff = '';

                    // Check for non-zero components and append to the string
                    if ($interval->y > 0) {
                        $diff .= $interval->y . ' years ';
                    }
                    if ($interval->m > 0) {
                        $diff .= $interval->m . ' months ';
                    }
                    if ($interval->d > 0) {
                        $diff .= $interval->d . ' days ';
                    }
                    if ($interval->h > 0) {
                        $diff .= $interval->h . ' hours ';
                    }
                    if ($interval->i > 0) {
                        $diff .= $interval->i . ' minutes ';
                    }
                    if ($interval->s > 0 || empty($diff)) {
                        $diff .= $interval->s . ' seconds ';
                    }

                    if (intval($diff) == 1) {
                        $dat_filter = 30;
                    } elseif (intval($diff) == 2) {
                        $dat_filter = 60;
                    } elseif (intval($diff) == 3) {
                        $dat_filter = 90;
                    } elseif (intval($diff) == 4) {
                        $dat_filter = 120;
                    } elseif (intval($diff) == 5) {
                        $dat_filter = 150;
                    } elseif (intval($diff) == 6) {
                        $dat_filter = 180;
                    } elseif (intval($diff) == 7) {
                        $dat_filter = 210;
                    } elseif (intval($diff) == 8) {
                        $dat_filter = 240;
                    } elseif (intval($diff) == 9) {
                        $dat_filter = 270;
                    } else {
                        $dat_filter = 720;
                    }
                }
            } else {
            }
        } else {
            switch ($mySelect) {
                case '24 Hours':
                    $dat_filter = 1;
                    $dateFilter = Carbon::now()->subHours(24);
                    break;
                case '7 Days':
                    $dateFilter = Carbon::now()->subDays(7);
                    $dat_filter = 7;
                    break;
                case '30 Days':
                    $dateFilter = Carbon::now()->subDays(30);

                    $dat_filter = 30;
                    break;
                case '60 Days':
                    $dateFilter = Carbon::now()->subDays(60);

                    $dat_filter = 60;
                    break;
                default:
                    // Default case, executed if none of the above cases match
                    $dat_filter = 1; // Set the default value here
                    $dateFilter = Carbon::now()->subDays(60);
                    break;
            }
            $unresolv = \App\Models\Tenant\Ticket::where('status', 'New')
                ->where('created_at', '>=',  now()->subDays($dat_filter))
                ->count();
        }
        // Output the difference

        // Customize your code here based on the selected value



        $received_tickets = Ticket::where('status','New')->where('created_at', '>=',  now()->subDays($dat_filter))->count();
        $received_tickets_sat = 0;
        $received_tickets_sun = 0;
        $received_tickets_mon = 0;
        $received_tickets_tue = 0;
        $received_tickets_wed = 0;
        $received_tickets_thu = 0;
        $received_tickets_fri = 0;
        $received_tickets_total = 0;


        $rec_t_sat = 0;
        $rec_t_sun = 0;
        $rec_t_mon = 0;
        $rec_t_tue = 0;
        $rec_t_wed = 0;
        $rec_t_thu = 0;
        $rec_t_fri = 0;

        $closed_tickets_sat = 0;
        $closed_tickets_sun = 0;
        $closed_tickets_mon = 0;
        $closed_tickets_tue = 0;
        $closed_tickets_wed = 0;
        $closed_tickets_thu = 0;
        $closed_tickets_fri = 0;
        $closed_tickets_total = 0;

        $open_tickets_sat = 0;
        $open_tickets_sun = 0;
        $open_tickets_mon = 0;
        $open_tickets_tue = 0;
        $open_tickets_wed = 0;
        $open_tickets_thu = 0;
        $open_tickets_fri = 0;
        $open_tickets_total = 0;
        $voice_tickets = Ticket::where('created_at', '>=', now()->subDays($dat_filter))->count();
        $resolved_tickets = Ticket::whereStatus('Resolved')->where('created_at', '>=', now()->subDays($dat_filter))->count();
        $unresolved_tickets = Ticket::where('status','!=','Resolved')->where('created_at', '>=', now()->subDays($dat_filter))->count();
        $resolved_tickets2 = 0;
        $unresolved_tickets2 = 0;
//        dd(now()->subDays($dat_filter));
        $sm_tickets = ResourceInfo::where('channel','!=','Whatsapp')->where('channel','!=','Livechat')->where('created_at', '>', now()->subDays($dat_filter))->count();
        $chat_tickets = ResourceInfo::where('channel','!=','Facebook')->where('channel','!=','Instagram')->where('channel','!=','Email')->where('channel','!=','Twitter')->where('channel','!=','Google')->where('created_at', '>', now()->subDays($dat_filter))->count();
        $New_Ticket_time = '';
        $Resolved_time = '';
        $useridd = '';
//        if ($social != null and $social != "") {
//            $sm_tickets = TicketAction::join('tickets', 'ticket_actions.ticket_id', '=', 'tickets.id')
//                ->join('resources', 'tickets.resource_id', '=', 'resources.id')
//                ->join('resource_infos', 'resources.id', '=', 'resource_infos.resource_id')
//                ->where('ticket_actions.created_at', '>=', now()->subDays($dat_filter))
//                ->where('resource_infos.channel', '=', $social)
//                ->where('tickets.status', '=', "New")
//                ->count();
//            // $sm_tickets = Resource::join('resource_infos', 'resources.id', '=', 'resource_infos.resource_id')
//            //     ->where('resources.status', 'New')
//            //     ->where('resources.created_at', '>=', now()->subDays($dat_filter))
//            //     ->where('resource_infos.channel', '=', $social)
//            //     ->count();
//        } else {
//            $sm_tickets = TicketAction::join('tickets', 'ticket_actions.ticket_id', '=', 'tickets.id')
//                ->join('resources', 'tickets.resource_id', '=', 'resources.id')
//                ->join('resource_infos', 'resources.id', '=', 'resource_infos.resource_id')
//                ->where('ticket_actions.created_at', '>=', now()->subDays($dat_filter))
//                ->where('tickets.status', '=', "New")
//                ->count();
//            // $sm_tickets = Resource::where('resources.status', 'New')
//            //     ->where('resources.created_at', '>=', now()->subDays($dat_filter))
//            //     ->count();
//        }
        // foreach ($data as $dat) {
        //     $sm_tickets++;
        //     if ($dat->status == "New") {
        //         $received_tickets++;
        //     }
        // }


        if ($social != "" and $social != null) {
            $data2 = TicketAction::join('tickets', 'ticket_actions.ticket_id', '=', 'tickets.id')
                ->join('resources', 'tickets.resource_id', '=', 'resources.id')
                ->join('resource_infos', 'resources.id', '=', 'resource_infos.resource_id')
                ->where('ticket_actions.created_at', '>=', now()->subDays($dat_filter))
                ->where('resource_infos.channel', '=', $social)
                ->get();
        } else {
            $data2 = TicketAction::where('created_at', '>=', now()->subDays($dat_filter))->get();
        }
//        foreach ($data2 as $dat2) {
//
//            if ($dat2->action == "New_Ticket") {
//                $unresolved_tickets++;
//            }
//            if ($dat2->action == "Resolved") {
//                $resolved_tickets++;
//                $unresolved_tickets--;
//            }
//        }

        if ($social != "" and $social != null) {
            $data36 = TicketAction::join('tickets', 'ticket_actions.ticket_id', '=', 'tickets.id')
                ->join('resources', 'tickets.resource_id', '=', 'resources.id')
                ->join('resource_infos', 'resources.id', '=', 'resource_infos.resource_id')
                ->where('ticket_actions.created_at', '>=', now()->subDays($dat_filter))
                ->where('resource_infos.channel', '=', $social)
                ->get();
        } else {
            $data36 = TicketAction::where('created_at', '>=', now()->subDays($dat_filter))->get();
        }
        $data36 = TicketAction::where('created_at', '>=', now()->subDays($dat_filter))->get();

        foreach ($data36 as $dat36) {

            if ($dat36->action == "New_Ticket") {
                $userid = $dat36->user_id;
                $userss = User::where("id", "=", $userid)->get();

                foreach ($userss as $us) {
                    $useridd = $us->username;
                }

                $unresolved_tickets2++;
            }
            if ($dat36->action == "Resolved") {
                $resolved_tickets2++;
                $unresolved_tickets2--;
            }
        }

        if ($social != "" and $social != null) {
            $data3 = ResourceAction::join('resource_infos', 'resource_actions.resource_id', '=', 'resource_infos.resource_id')
                ->where('action', '=', 'New_Ticket')
                ->orWhere('action', '=', 'Resolved')
                ->where('resource_infos.channel', '=', $social)
                ->where('resource_actions.created_at', '>=', now()->subDays($dat_filter))
                ->get();
        } else {
            $data3 = ResourceAction::where('action', '=', 'New_Ticket')->orWhere('action', '=', 'Resolved')
                ->where('created_at', '>=', now()->subDays($dat_filter))->get();
        }


        foreach ($data3 as $dat3) {

            $date_day = Carbon::parse($dat3->created_at);
            $dayName = $date_day->format('l');

            if ($dayName == "Saturday") {
                $received_tickets_sat++;
                $received_tickets_total++;
            } elseif ($dayName == "Sunday") {
                $received_tickets_sun++;
                $received_tickets_total++;
            } elseif ($dayName == "Monday") {
                $received_tickets_mon++;
                $received_tickets_total++;
            } elseif ($dayName == "Tuesday") {
                $received_tickets_tue++;
                $received_tickets_total++;
            } elseif ($dayName == "Wednesday") {
                $received_tickets_wed++;
                $received_tickets_total++;
            } elseif ($dayName == "Thursday") {
                $received_tickets_thu++;
                $received_tickets_total++;
            } elseif ($dayName == "Friday") {
                $received_tickets_fri++;
                $received_tickets_total++;
            }
        }
        //over_here
        try {
            $all_agents = User::query()->with('agentRoles')->whereHas('roles', function ($q) {
                $q->where('allowed_route', 'agent');
            });

            foreach ($all_agents->get() as $agent) {
                $username = $agent->username;
            }

            $data411 = ResourceAction::where('action', '=', 'New_Ticket')
                ->where('user_id', '=', $username)
                ->first();

            if ($data411 !== null) {
                // Access the properties of the record
                $New_Ticket_time2 = $data411->updated_at;
            } else {
                $New_Ticket_time2 = "";

                // Handle the case when no matching records are found
            }

            $data5 = ResourceAction::where('action', '=', 'Resolved')
                ->where('user_id', '=', $username)
                ->latest()
                ->get();

            if ($data5->isNotEmpty()) {
                // Access the properties of the first record or iterate over the collection
                $Resolved_time2 = $data5->first()->updated_at;
            } else {

                $Resolved_time2 = "";

                // Handle the case when no matching records are found
            }

            if ($Resolved_time2 != "" and $New_Ticket_time2 != "") {


                $startDate2 = Carbon::parse($New_Ticket_time2);
                $endDate2 = Carbon::parse($Resolved_time2);

                $diffInHours2 = $startDate2->diffInHours($endDate2);
                $diffInMinutes2 = $startDate2->diffInMinutes($endDate2);
                $diffInSec2 = $startDate2->diffInSeconds($endDate2);

                $remainingSec2 = $diffInSec2 % 60;
                $totalMinutes2 = $diffInMinutes2 + floor($diffInSec2 / 60);

                if ($totalMinutes2 >= 60) {
                    $extraHours2 = floor($totalMinutes2 / 60);
                    $totalMinutes2 = $totalMinutes2 % 60;
                    $diffInHours2 += $extraHours2;
                }

                $output2 = sprintf("%02d:%02d:%02d", $diffInHours2, $totalMinutes2, $remainingSec2);
            } else {

                $output2 = "00:00:00";
            }
            // dd($all_agents);
        } catch (\Exception $e) {
            // $this->alert('error', 'Something was wrong !', [
            //     'timerProgressBar' => true,
            // 'showCloseButton' => true,
            //     'timer' => '6000',
            // ]);
        }
        $startDate2 = Carbon::parse($New_Ticket_time);
        $endDate2 = Carbon::parse($Resolved_time);

        // $diffInDays = $startDate->diffInDays($endDate);
        $diffInHours = $startDate2->diffInHours($endDate2);
        $diffInMinutes = $startDate2->diffInMinutes($endDate2);
        $diffInSec = $startDate2->diffInSeconds($endDate2);



        $remainingSec = $diffInSec % 60;
        $totalMinutes = $diffInMinutes + floor($diffInSec / 60) + floor($remainingSec / 60);

        if ($remainingSec > 0 && $remainingSec <= 59) {
            $output = $totalMinutes . "m," . $remainingSec . "s";
            // $output2 = "7:25:" . $remainingSec;
        } else {
            $output = $totalMinutes . "m";
            // $output2 = $totalMinutes . ":00:00";
        }

        if ($social != "" and $social != null) {
            $data4 = ResourceAction::join('resource_infos', 'resource_actions.resource_id', '=', 'resource_infos.resource_id')
                ->where('resource_actions.action', '=', 'New_Ticket')
                ->where('resource_infos.channel', '=', $social)
                ->where('resource_actions.created_at', '>=', now()->subDays($dat_filter))
                ->first();
        } else {
            $data4 = ResourceAction::where('action', '=', 'New_Ticket')
                ->where('created_at', '>=', now()->subDays($dat_filter))
                ->first();
        }
        if ($data4 !== null) {
            // Access the properties of the record
            $New_Ticket_time = $data4->updated_at;
        } else {
            // Handle the case when no matching records are found
        }
        if ($social != "" and $social != null) {
            $data5 = ResourceAction::join('resource_infos', 'resource_actions.resource_id', '=', 'resource_infos.resource_id')
                ->where('resource_actions.action', '=', 'Resolved')
                ->where('resource_actions.created_at', '>=', now()->subDays($dat_filter))
                ->where('resource_infos.channel', '=', $social)
                ->latest('resource_actions.created_at')
                ->get();
        } else {
            $data5 = ResourceAction::where('action', '=', 'Resolved')
                ->where('created_at', '>=', now()->subDays($dat_filter))
                ->latest()
                ->get();
        }
        // $data5 = ResourceAction::where('action', '=', 'Resolved')
        //     ->where('created_at', '>=', now()->subDays($dat_filter))
        //     ->latest()
        //     ->get();

        if ($data5->isNotEmpty()) {
            // Access the properties of the first record or iterate over the collection
            $Resolved_time = $data5->first()->updated_at;
        } else {
            // Handle the case when no matching records are found
        }
        if ($social != "" and $social != null) {
            $data6 = ResourceAction::join('resource_infos', 'resource_actions.resource_id', '=', 'resource_infos.resource_id')
                ->where('resource_infos.channel', '=', $social)
                ->where('resource_actions.action', '=', 'New_Ticket')
                ->orWhere('resource_actions.action', '=', 'Closed')
                ->where('resource_actions.created_at', '>=', now()->subDays($dat_filter))->get();
        } else {
            $data6 = ResourceAction::where('action', '=', 'New_Ticket')->orWhere('action', '=', 'Closed')
                ->where('created_at', '>=', now()->subDays($dat_filter))->get();
        }
        foreach ($data6 as $dat6) {
            if ($dat6->action == "New_Ticket") {
                $date_day2 = Carbon::parse($dat6->created_at);
                $dayName2 = $date_day2->format('l');

                if ($dayName2 == "Saturday") {
                    $open_tickets_sat++;
                    $open_tickets_total++;
                } elseif ($dayName2 == "Sunday") {
                    $open_tickets_sun++;
                    $open_tickets_total++;
                } elseif ($dayName2 == "Monday") {
                    $open_tickets_mon++;
                    $open_tickets_total++;
                } elseif ($dayName2 == "Tuesday") {
                    $open_tickets_tue++;
                    $open_tickets_total++;
                } elseif ($dayName2 == "Wednesday") {
                    $open_tickets_wed++;
                    $open_tickets_total++;
                } elseif ($dayName2 == "Thursday") {
                    $open_tickets_thu++;
                    $open_tickets_total++;
                } elseif ($dayName2 == "Friday") {
                    $open_tickets_fri++;
                    $open_tickets_total++;
                }
            } else {
                $date_day3 = Carbon::parse($dat6->created_at);
                $dayName3 = $date_day3->format('l');
                if ($dayName3 == "Saturday") {
                    $closed_tickets_sat++;
                    $closed_tickets_total++;
                } elseif ($dayName3 == "Sunday") {
                    $closed_tickets_sun++;
                    $closed_tickets_total++;
                } elseif ($dayName3 == "Monday") {
                    $closed_tickets_mon++;
                    $closed_tickets_total++;
                } elseif ($dayName3 == "Tuesday") {
                    $closed_tickets_tue++;
                    $closed_tickets_total++;
                } elseif ($dayName3 == "Wednesday") {
                    $closed_tickets_wed++;
                    $closed_tickets_total++;
                } elseif ($dayName3 == "Thursday") {
                    $closed_tickets_thu++;
                    $closed_tickets_total++;
                } elseif ($dayName3 == "Friday") {
                    $closed_tickets_fri++;
                    $closed_tickets_total++;
                }
            }
        }


        if ($social != "" and $social != null) {
            $data30 = ResourceAction::join('resources', 'resource_actions.resource_id', '=', 'resources.id')
                ->join('resource_infos', 'resources.id', '=', 'resource_infos.resource_id')
                ->where('resource_actions.action', 'New')
                ->where('resource_infos.channel', '=', $social)
                ->where('resource_actions.created_at', '>=', now()->subDays($dat_filter))
                ->get();
        } else {
            $data30 = ResourceAction::where('action', 'New')->where('created_at', '>=', now()->subDays($dat_filter))->get();
        }

        $rec_t_sat = 0;
        $rec_t_sun = 0;
        $rec_t_mon = 0;
        $rec_t_tue = 0;
        $rec_t_wed = 0;
        $rec_t_thu = 0;
        $rec_t_fri = 0;

        foreach ($data30 as $dat) {
            $dayName = $dat->created_at->format('l');
            switch ($dayName) {
                case 'Saturday':
                    $rec_t_sat++;
                    break;
                case 'Sunday':
                    $rec_t_sun++;
                    break;
                case 'Monday':
                    $rec_t_mon++;
                    break;
                case 'Tuesday':
                    $rec_t_tue++;
                    break;
                case 'Wednesday':
                    $rec_t_wed++;
                    break;
                case 'Thursday':
                    $rec_t_thu++;
                    break;
                case 'Friday':
                    $rec_t_fri++;
                    break;
            }
        }


        $startDate = Carbon::parse($New_Ticket_time);
        $endDate = Carbon::parse($Resolved_time);

        // $diffInDays = $startDate->diffInDays($endDate);
        $diffInHours = $startDate->diffInHours($endDate);
        $diffInMinutes = $startDate->diffInMinutes($endDate);
        $diffInSec = $startDate->diffInSeconds($endDate);



        $remainingSec = $diffInSec % 60;
        $totalMinutes = $diffInMinutes + floor($diffInSec / 60) + floor($remainingSec / 60);

        if ($remainingSec > 0 && $remainingSec <= 59) {
            $output = $totalMinutes . "m," . $remainingSec . "s";
            $output2 = "7:25:" . $remainingSec;
        } else {
            $output = $totalMinutes . "m";
            $output2 = $totalMinutes . ":00:00";
        }



        // echo $totalMinutes;

        return view('tenant.client.analytics.index', [
            'received_tickets' => $received_tickets,
            'voice_tickets' => $voice_tickets,
            'resolved_tickets' => $resolved_tickets,
            'unresolved_tickets' => $unresolved_tickets,
            'sm_tickets' => $sm_tickets,
            'chat_tickets' => $chat_tickets,
            'output' => $output,
            'output2' => $output2,
            'all_agents' => $all_agents,
            'diffInHours' => $diffInHours,
            'diffInMinutes' => $diffInMinutes,
            'received_tickets_sat' => $received_tickets_sat,
            'received_tickets_sun' => $received_tickets_sun,
            'received_tickets_mon' => $received_tickets_mon,
            'received_tickets_tue' => $received_tickets_tue,
            'received_tickets_wed' => $received_tickets_wed,
            'received_tickets_thu' => $received_tickets_thu,
            'received_tickets_fri' => $received_tickets_fri,
            'closed_tickets_sat' =>  $closed_tickets_sat,
            'closed_tickets_sun' =>  $closed_tickets_sun,
            'closed_tickets_mon' =>  $closed_tickets_mon,
            'closed_tickets_tue' =>  $closed_tickets_tue,
            'closed_tickets_wed' =>  $closed_tickets_wed,
            'closed_tickets_thu' =>  $closed_tickets_thu,
            'closed_tickets_fri' =>  $closed_tickets_fri,
            'open_tickets_sat' =>  $open_tickets_sat,
            'open_tickets_sun' =>  $open_tickets_sun,
            'open_tickets_mon' =>  $open_tickets_mon,
            'open_tickets_tue' =>  $open_tickets_tue,
            'open_tickets_wed' =>  $open_tickets_wed,
            'open_tickets_thu' =>  $open_tickets_thu,
            'open_tickets_fri' =>  $open_tickets_fri,
            'rec_t_sat' =>  $rec_t_sat,
            'rec_t_sun' =>  $rec_t_sun,
            'rec_t_mon' =>  $rec_t_mon,
            'rec_t_tue' =>  $rec_t_tue,
            'rec_t_wed' =>  $rec_t_wed,
            'rec_t_thu' =>  $rec_t_thu,
            'rec_t_fri' =>  $rec_t_fri,
            'data36' =>  $data36,
            'unresolved_tickets2' =>  $unresolved_tickets2,
            'resolved_tickets2' =>  $resolved_tickets2,
            'closed_tickets_total' =>   $closed_tickets_total,
            'open_tickets_total' =>   $open_tickets_total,
            'received_tickets_total' =>   $received_tickets_total,
            'useridd' =>   $useridd,
            'unresolv' =>   $unresolv,


        ], compact('data30', 'rec_t_sat', 'rec_t_sun', 'rec_t_mon', 'rec_t_tue', 'rec_t_wed', 'rec_t_thu', 'rec_t_fri'));

        return response()->json($diff);
    }
}
