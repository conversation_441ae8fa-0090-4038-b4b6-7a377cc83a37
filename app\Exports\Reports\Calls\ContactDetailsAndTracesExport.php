<?php

namespace App\Exports\Reports\Calls;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ContactDetailsAndTracesExport implements FromCollection, WithHeadings
{
    protected $records;
    protected $headings;

    public function __construct($records, $headings)
    {
        $this->records = $records;
        $this->headings = $headings;
    }

    public function collection()
{
    return $this->records->map(function ($record) {
        return [
            'agent_name' => $record['agent_name'],
            'interaction' => $record['interaction'],
            'day' => $record['day'],
            'offered' => $record['offered'] === 0 ? '0' : $record['offered'],
            'accepted' => $record['accepted'] === 0 ? '0' : $record['accepted'],
            'not_accepted' => $record['not_accepted'] === 0 ? '0' : $record['not_accepted'],
            'rejected' => $record['rejected'] === 0 ? '0' : $record['rejected'],
            'handle_time' => $record['handle_time'] === 0 ? '00:00:00' : gmdate('H:i:s', $record['handle_time']),
            'avg_handle_time' => $record['avg_handle_time'] === 0 ? '00:00:00' : gmdate('H:i:s', $record['avg_handle_time']),
            'invite_time' => $record['invite_time'] === 0 ? '00:00:00' : gmdate('H:i:s', $record['invite_time']),
            'max_invite' => $record['max_invite'] === 0 ? '00:00:00' : gmdate('H:i:s', $record['max_invite']),
            'avg_invite_time' => $record['avg_invite_time'] === 0 ? '00:00:00' : gmdate('H:i:s', $record['avg_invite_time']),
            'total_engage' => $record['total_engage'] === 0 ? '00:00:00' : gmdate('H:i:s', $record['total_engage']),
            'total_hold' => $record['total_hold'] === 0 ? '00:00:00' : gmdate('H:i:s', $record['total_hold']),
            'total_wrap' => $record['total_wrap'] === 0 ? '00:00:00' : gmdate('H:i:s', $record['total_wrap']),
            'avg_engage' => $record['avg_engage'] === 0 ? '00:00:00' : gmdate('H:i:s', $record['avg_engage']),
            'avg_hold' => $record['avg_hold'] === 0 ? '00:00:00' : gmdate('H:i:s', $record['avg_hold']),
            'avg_wrap' => $record['avg_wrap'] === 0 ? '00:00:00' : gmdate('H:i:s', $record['avg_wrap']),
        ];
    });
}
    public function headings(): array
    {
        return $this->headings;
    }
} 