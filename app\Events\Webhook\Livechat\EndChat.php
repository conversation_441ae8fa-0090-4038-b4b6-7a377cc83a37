<?php

namespace App\Events\Webhook\Livechat;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class EndChat implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $tenant_id;
    public $sessionToken;

    /**
     * Create a new event instance.
     */
    public function __construct($tenant_id, $sessionToken)
    {
        $this->tenant_id    =   $tenant_id;
        $this->sessionToken      =   $sessionToken;
    }

    public function broadcastWith()
    {
        return [
            'tenant_id'     =>$this->tenant_id,
            'content'       =>$this->sessionToken,

        ];
    }

    public function broadcastOn()
    {
        return new Channel('EndChat.'.$this->tenant_id);
    }
}
