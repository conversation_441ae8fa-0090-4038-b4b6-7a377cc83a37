<?php

namespace App\Exports;
use Carbon\Carbon;
use App\Models\Tenant\User;
use App\Models\Tenant\WfmBreak;
use App\Models\Tenant\WfmScheduleLog;
use Illuminate\Support\Facades\Request;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;



class ChangeSchedulingExport implements FromCollection, WithHeadings
{
   
    /**
    * @return \Illuminate\Support\Collection
    */
    public $defaultAccount;
    public function collection()
    {
        
        $result= WfmScheduleLog::query()->with('user' , 'shift' , 'status')->get();
        $url = Request::url();
        $this->defaultAccount = explode('.', parse_url($url, PHP_URL_HOST))[0];

        return $result->map(function ($item) {

            return [
                'Name' => $item->User->first_name,
                'User Name' => $item->User->username,
                'DATE' => $item->date,
                'New Skill Group' => $item->wfmUserSkill->wfmSkillGroup->title,
                'Old Skill Group' => $item->wfmSehcdule->wfmUserSkill->wfmSkillGroup->title,
                'New Skill' => $item->wfmUserSkill->wfmSkill->skill_name,
                'Old Skill' => $item->wfmSehcdule->wfmUserSkill->wfmSkill->skill_name,
                'New Shift' =>' '.Carbon::parse($item->Shift->from)->format('H:i').'-'.Carbon::parse($item->Shift->from)->format('H:i').'' ,
                'Old Shift' => ''.Carbon::parse($item->wfmSehcdule->Shift->from)->format('H:i').'-'.Carbon::parse($item->wfmSehcdule->Shift->from)->format('H:i').'',
                'New Status' => $item->status->status_type,
                'Old Status' => $item->wfmSehcdule->status->status_type,
            ];
       
        });
        
    }

    public function headings(): array
    {
            return [
                'Name' ,
                'User Name',
                'DATE' ,
                'New Skill Group' ,
                'Old Skill Group' ,
                'Old Skill' ,
                '>New Shift' ,
                'Old Shift',
                'New Status' ,
                'Old Status' ,
        ];
      
      
    }
}
