<?php

namespace App\Http\Controllers\Tenant\Admin\Flows;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Category;
use App\Models\Tenant\CategoryLevel;
use App\Models\Tenant\Department;
use App\Models\Tenant\Distribution;
use App\Models\Tenant\FormGroup;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\File;

class MainController extends Controller
{
    public function index()
    {
        return view('tenant.admin.flows.index');
    }

    public function import()
    {
        return view('tenant.admin.flows.import');
    }

    public function store(Request $request)
    {

        $validation = Validator::make($request->all(), [
            'file' => 'required|file', // Max size in kilobytes (10 MB)
        ], [
            'file.file' => 'The file must be a JSON file.',
        ]);

        if ($validation->fails()) {
            return redirect()->back()->withErrors($validation)->withInput();
        }

        $file = $request->file('file');

        // Get the file extension
        $extension = $file->getClientOriginalExtension();

        // Check the file type
        if ($extension != 'json') {
            return back()->with('success', 'The file must be a JSON file..');
        }


        $content = File::get($file->getPathname());


        $json = json_decode($content);

        if (!isset($json->departments) || !isset($json->categoryLevels) || !isset($json->categories) || !isset($json->formGroups) || !isset($json->distributions))
        {
            return back()->with('errors2', 'The file must be a JSON file like a demo data');
        }

        $departments = $json->departments;
        $categoryLevels = $json->categoryLevels;
        $categories = $json->categories;
        $formGroups = $json->formGroups;
        $distributions = $json->distributions;


        try {
            Department::query()->delete();
            CategoryLevel::query()->delete();
            Category::query()->delete();
            FormGroup::query()->delete();
            Distribution::query()->delete();

            foreach ($departments as $department)
            {

                Department::create(['name'=>$department->title,'status'=>1,'description'=>$department->title]);
            }

            foreach ($categoryLevels as $categoryLevel)
            {
                CategoryLevel::create(['level'=>$categoryLevel->level,'label'=>$categoryLevel->label,'status'=>1]);
            }

            foreach ($categories as $category)
            {
                if ($category->parent == "" || $category->parent == null )
                {
                    $categoryLevelId = CategoryLevel::where('level',$category->level)->first();

                    Category::create(['id'=>$category->id,'name'=>$category->title,'status'=>1,'category_level_id'=>$categoryLevelId->id]);
                } else
                {
                    $categoryLevelId = CategoryLevel::where('level',$category->level)->first();
                    $categoryId = Category::where('id',$category->parent)->first();

                    Category::create(['id'=>$category->id,'name'=>$category->title,'category_level_id'=>$categoryLevelId->id,'status'=>1,'parent_id'=>$categoryId->id]);
                }
            }

            foreach ($formGroups as $formGroup)
            {
                FormGroup::create(['name'=>$formGroup->title,'status'=>1,'description'=>$formGroup->title]);

            }

            $distributionData = [];

            foreach ($distributions as $distribution) {
                try {
                    $getDepartment  = Department::where('name', $distribution->department)->first();
                    $getCategory    = Category::where('id', $distribution->category)->first();
                    $getFormGroup   = FormGroup::where('name', $distribution->formGroup)->first();

                    // Build the data array for each distribution

                    if($getDepartment != null && $getCategory != null  && $getFormGroup != null )
                    {
                        $distributionData[] = [
                            'department_id' => $getDepartment->id,
                            'category_id'   => $getCategory->id,
                            'form_group_id' => $getFormGroup->id,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ];
                    }else
                    {
                        $data = [$distribution->department, $distribution->category, $distribution->formGroup] ;
                        dd($data);
                    }

                }catch (\Exception $e)
                {
                    dd($e);
                }

            }


            $chunkSize = 500;


            $totalChunks = ceil(count($distributionData) / $chunkSize);


            for ($i = 0; $i < $totalChunks; $i++) {
                $start = $i * $chunkSize;
                $chunk = array_slice($distributionData, $start, $chunkSize);

                Distribution::insert($chunk);
            }

        }
        catch (\Exception $e)
        {

        }



        return back()->with('success', 'JSON file uploaded successfully.');



    }
}
