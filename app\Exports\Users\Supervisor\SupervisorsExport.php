<?php

namespace App\Exports\Users\Supervisor;

use App\Models\Tenant\User;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class SupervisorsExport implements FromCollection, WithHeadings, WithMapping
{
    /**
    * @return \Illuminate\Support\Collection
    */

    protected $createdAtFrom;
    protected $createdAtTo;
    protected $export_role_id;
    protected $export_status;

    public function __construct($createdAtFrom, $createdAtTo, $export_role_id, $export_status)
    {
        $this->createdAtFrom = $createdAtFrom;
        $this->createdAtTo = $createdAtTo;
        $this->export_role_id = $export_role_id;
        $this->export_status = $export_status;
    }

    public function collection()
    {
        return User::query()
            ->when($this->export_role_id, function ($query) {
                return $query->whereHas('role', function ($q) {
                    $q->where('id', $this->export_role_id);
                });
            })
            ->when($this->createdAtFrom && $this->createdAtTo, function ($query) {
                return $query->where('created_at', '>=', $this->createdAtFrom.' 00:00:00')
                             ->where('created_at', '<=', $this->createdAtTo.' 23:59:59');
            })
            ->when($this->export_status, function ($query) {
            
                return $query->where('status', $this->export_status == 'Active' ? 1 : 0);
            })
            ->get();
    }
    public function map($user): array
    {
        return [
            $user->first_name,            // First Name
            $user->last_name,             // Last Name
            $user->username,              // Username
            $user->mobile,                // Mobile
            $user->email,                 // Email
            $user->role[0]->name ?? 'N/A',   // Role (assuming a relationship with the role model)
            $user->departments->pluck('name')->implode(', ') ?? 'N/A', // Departments (assuming a many-to-many relationship with departments)
            $user->status(),                // Status
            $user->created_at->format('Y-m-d H:i:s'), // Created At formatted as DateTime
        ];
    }

    public function headings(): array
    {
        return [
            'First Name',
            'Last Name',
            'Username',
            'Mobile',
            'Email',
            'Role',
            'Departments',
            'Status',
            'Created At',
            // Add any other headings you want to export
        ];
    }
}
