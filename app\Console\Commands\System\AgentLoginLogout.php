<?php

namespace App\Console\Commands\System;

use App\Models\Tenant;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;

class AgentLoginLogout extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'agent:logout';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $tenants = Tenant::with('domains')->get();

            foreach ($tenants as $tenant)
            {
                foreach ($tenant->domains as $domain)
                {
                    Http::get('https://'.$domain->domain.'/webhook/agent/logout');
                }
//                Http::get('https://'.$tenant->domains[0]->domain.'/webhook/agent/logout');
            }
        }catch (\Exception $e)
        {
//            Mail::raw($e->getMessage(), function ($message) {
//                $message->to('<EMAIL>')->subject('Exception Agent logout | agent:logout');
//            });
        }

    }
}
