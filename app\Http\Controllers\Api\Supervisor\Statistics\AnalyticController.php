<?php

namespace App\Http\Controllers\Api\Supervisor\Statistics;

use App\Http\Controllers\Controller;
use App\Http\Resources\Supervisor\Monitoring\AvgTotalTimeResource;
use App\Http\Resources\Supervisor\Statistics\Dashboard\nonVoiceBehavior;
use App\Http\Resources\Supervisor\Statistics\Dashboard\ticketBehavior;
use App\Models\Tenant\AgentStatusAction;
use App\Models\Tenant\Resource;
use App\Models\Tenant\Ticket;
use DateTime;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class AnalyticController extends Controller
{

    public function nonVoiceBehavior(Request $request)
    {
        $channel = isset($request->channel) && $request->channel != '' ? $request->channel : null;
        $fromDate = isset($request->fromDate) && $request->fromDate != '' ? $request->fromDate : date('Y-m-d');
        $toDate = isset($request->toDate) && $request->toDate != '' ? $request->toDate : date('Y-m-d');

        $resources = Resource::with(['resourceInfo']);

        if ($channel != null) {
            $channel = Str::ucfirst(Str::lower($channel));
            $resources->whereHas('resourceInfo', function ($query) use ($channel) {
                $query->where('channel', 'like', '%' . $channel . '%');
            });
        }

        if ($fromDate != null) {
            $fromDate = date($fromDate . ' 00:00:00');
            $resources->where('created_at', '>=', $fromDate);
        }

        if ($toDate != null) {
            $toDate = date($toDate . ' 23:59:59');
            $resources->where('created_at', '<=', $toDate);
        }

        $today = Carbon::today();

        $new = clone $resources;
        $new = $new->where('status', 'New')->count();

        $resolved = clone $resources;
        $resolved = $resolved->where('status', 'Resolved')->count();

        $closed = clone $resources;
        $closed = $closed->where('status', 'Closed')->count();

        $escalated = clone $resources;
        $escalated = $escalated->where('status', 'New Ticket')->count();

        $unresolved = clone $resources;
        $unresolved = $unresolved->where('status', '!=','Resolved')->count();

        $received = clone $resources;
        $received = $received->whereDate('created_at', $today)->count();

        $overDue = clone $resources;
        $overDue = $overDue->where('status', 'New')->count();

        $all = [
            'new' => ['total'=>$new ,'fontawesome'=>'fa'],
            'escalated' => ['total'=>$escalated ,'fontawesome'=>'fa'],
            'resolved' => ['total'=>$resolved ,'fontawesome'=>'fa'],
            'closed' => ['total'=>$closed ,'fontawesome'=>'fa'],
            'unresolved' => ['total'=>$unresolved ,'fontawesome'=>'fa'],
            'received' => ['total'=>$received ,'fontawesome'=>'fa'],
            'overDue' => ['total'=>$overDue ,'fontawesome'=>'fa'],
        ];

        return new nonVoiceBehavior($all);

    }

    public function ticketBehavior(Request $request)
    {
        $channel = isset($request->channel) && $request->channel != '' ? $request->channel : null;
        $fromDate = isset($request->fromDate) && $request->fromDate != '' ? $request->fromDate : null;
        $toDate = isset($request->toDate) && $request->toDate != '' ? $request->toDate : null;

        $resources = Ticket::with(['ticketInfo']);

        if ($channel != null) {
            $channel = Str::ucfirst(Str::lower($channel));
            $resources->whereHas('ticketInfo', function ($query) use ($channel) {
                $query->where('channel', 'like', '%' . $channel . '%');
            });
        }

        if ($fromDate != null) {
            $fromDate = date($fromDate . ' 00:00:00');
            $resources->where('created_at', '>=', $fromDate);
        }

        if ($toDate != null) {
            $toDate = date($toDate . ' 23:59:59');
            $resources->where('created_at', '<=', $toDate);
        }


        $new = clone $resources;
        $new = $new->where('status', 'New')->count();

        $resolved = clone $resources;
        $resolved = $resolved->where('status', 'Resolved')->count();

        $closed = clone $resources;
        $closed = $closed->where('status', 'Closed')->count();

        $redirect = clone $resources;
        $redirect = $redirect->where('status', 'Redirect')->count();

        $unresolved = clone $resources;
        $unresolved = $unresolved->where('status', '!=','Resolved')->count();

        $received = clone $resources;
        $received = $received->count();



        $all = [
            'new' => ['total'=>$new ,'fontawesome'=>'fa'],
            'redirect' => ['total'=>$redirect ,'fontawesome'=>'fa'],
            'resolved' => ['total'=>$resolved ,'fontawesome'=>'fa'],
            'closed' => ['total'=>$closed ,'fontawesome'=>'fa'],
            'unresolved' => ['total'=>$unresolved ,'fontawesome'=>'fa'],
            'received' => ['total'=>$received ,'fontawesome'=>'fa'],
        ];

        return new ticketBehavior($all);

    }

    public function art(Request $request)
    {
        $channel = isset($request->channel) && $request->channel != '' ? $request->channel : null;
        $fromDate = isset($request->fromDate) && $request->fromDate != '' ? $request->fromDate : null;
        $toDate = isset($request->toDate) && $request->toDate != '' ? $request->toDate : null;

        $resources = Ticket::with(['ticketInfo']);

        if ($channel != null) {
            $channel = Str::ucfirst(Str::lower($channel));
            $resources->whereHas('ticketInfo', function ($query) use ($channel) {
                $query->where('channel', 'like', '%' . $channel . '%');
            });
        }

        if ($fromDate != null) {
            $fromDate = date($fromDate . ' 00:00:00');
            $resources->where('created_at', '>=', $fromDate);
        }

        if ($toDate != null) {
            $toDate = date($toDate . ' 23:59:59');
            $resources->where('created_at', '<=', $toDate);
        }


        // Calculate the total time difference between created_at and updated_at
        $resources = $resources->get()->sum(function ($resource) {return $resource->updated_at->diffInSeconds($resource->created_at);});



        return new AvgTotalTimeResource(['aggregate_resolution_time'=>$resources]);
    }

    public function interacted(Request $request){
        try {
            $channel = isset($request->channel) && $request->channel != '' ? $request->channel : null;
            $fromDate = isset($request->fromDate) && $request->fromDate != '' ? $request->fromDate : null;
            $toDate = isset($request->toDate) && $request->toDate != '' ? $request->toDate : null;

            $resources = Ticket::with(['ticketInfo']);

            if ($channel != null) {
                $channel = Str::ucfirst(Str::lower($channel));
                $resources->whereHas('ticketInfo', function ($query) use ($channel) {
                    $query->where('channel', 'like', '%' . $channel . '%');
                });
            }

            if ($fromDate != null) {
                $fromDate = date($fromDate . ' 00:00:00');
                $resources->where('created_at', '>=', $fromDate);
            }else
            {
                $fromDate = Carbon::today();
            }

            if ($toDate != null) {
                $toDate = date($toDate . ' 23:59:59');
            }else{
                $toDate = Carbon::today();
            }


            if ($fromDate != null && $toDate != null) {
                // Calculate the difference in days
                $dateDiff = (new DateTime($fromDate))->diff(new DateTime($toDate))->days;

                // Generate all hours of the day
                $allHours = $this->generateAllHours();

                // Generate all days between $fromDate and $toDate
                $allDays = $this->generateAllDays($fromDate, $toDate);

                // Generate all weeks between $fromDate and $toDate
                $allWeeks = $this->generateAllWeeks($fromDate, $toDate);

                // Generate all months between $fromDate and $toDate
                $allMonths = $this->generateAllMonths($fromDate, $toDate);

                // Determine the filter type based on the difference
                if ($dateDiff <= 1) {
                    // Filter type: day
                    $filteredData = $this->fillMissingHours($this->getTicketByDateNew($fromDate), $allHours);
                    $filteredNoneData = $this->fillNoneMissingHours($this->getTicketByDateNoneNew($fromDate), $allHours);

                } elseif ($dateDiff > 1 && $dateDiff <= 7) {
                    // Filter type: week
                    $filteredData = $this->fillMissingDays($this->getTicketByDateNew($fromDate), $allDays);
                    $filteredNoneData = $this->fillNoneMissingDays($this->getTicketByDateNoneNew($fromDate), $allDays);
                } elseif ($dateDiff > 7 && $dateDiff < 30) {
                    // Filter type: month
                    $filteredData = $this->fillMissingWeeks($this->getTicketByDateNew($fromDate), $allWeeks);
                    $filteredNoneData = $this->fillNoneMissingWeeks($this->getTicketByDateNoneNew($fromDate), $allWeeks);
                } else {
                    // Filter type: year
                    $filteredData = $this->fillMissingMonths($this->getTicketByDateNew($fromDate), $allMonths);
                    $filteredNoneData = $this->fillNoneMissingMonths($this->getTicketByDateNoneNew($fromDate), $allMonths);
                }

                return response()->json(['data' =>
                    [

                            'Received'=>$filteredData,
                            'interactedWith'=>$filteredNoneData

                    ]
                ]);
            }

        }catch (\Exception $e)
        {
            return response()->json(['error' => true , 'message'=>'Something Went Wrong !'],400);
        }

    }

    public function interactedDetails(Request $request){
        try {
            $channel = isset($request->channel) && $request->channel != '' ? $request->channel : null;
            $fromDate = isset($request->fromDate) && $request->fromDate != '' ? $request->fromDate : null;
            $toDate = isset($request->toDate) && $request->toDate != '' ? $request->toDate : null;

            $resources = Ticket::with(['ticketInfo']);

            if ($channel != null) {
                $channel = Str::ucfirst(Str::lower($channel));
                $resources->whereHas('ticketInfo', function ($query) use ($channel) {
                    $query->where('channel', 'like', '%' . $channel . '%');
                });
            }

            if ($fromDate != null) {
                $fromDate = date($fromDate . ' 00:00:00');
                $resources->where('created_at', '>=', $fromDate);
            }else
            {
                $fromDate = Carbon::today();
            }

            if ($toDate != null) {
                $toDate = date($toDate . ' 23:59:59');
            }else{
                $toDate = Carbon::today();
            }

            if ($fromDate != null && $toDate != null) {
                // Calculate the difference in days
                $dateDiff = (new DateTime($fromDate))->diff(new DateTime($toDate))->days;

                // Generate all hours of the day
                $allHours = $this->generateAllHours();

                // Generate all days between $fromDate and $toDate
                $allDays = $this->generateAllDays($fromDate, $toDate);

                // Generate all weeks between $fromDate and $toDate
                $allWeeks = $this->generateAllWeeks($fromDate, $toDate);

                // Generate all months between $fromDate and $toDate
                $allMonths = $this->generateAllMonths($fromDate, $toDate);



                // Determine the filter type based on the difference
                if ($dateDiff <= 1) {
                    // Filter type: day
                    $newFilteredData                = $this->fillMissingHours($this->getTicketByDateNew($fromDate), $allHours);
                    $resolvedFilteredNoneData       = $this->fillMissingHoursResolved($this->getTicketByDateResolved($fromDate), $allHours);
                    $closedFilteredNoneData         = $this->fillMissingHoursClosed($this->getTicketByDateClosed($fromDate), $allHours);
                    $redirectedFilteredNoneData     = $this->fillMissingHoursRedirected($this->getTicketByDateRedirected($fromDate), $allHours);
                    $reopenFilteredNoneData         = $this->fillMissingHoursReopen($this->getTicketByDateReopen($fromDate), $allHours);

                } elseif ($dateDiff > 1 && $dateDiff <= 7) {
                    // Filter type: week
                    $newFilteredData                = $this->fillMissingDays($this->getTicketByDateNew($fromDate), $allDays);
                    $resolvedFilteredNoneData       = $this->fillMissingDaysResolved($this->getTicketByDateResolved($fromDate), $allDays);
                    $closedFilteredNoneData         = $this->fillMissingDaysClosed($this->getTicketByDateClosed($fromDate), $allDays);
                    $redirectedFilteredNoneData     = $this->fillMissingDaysRedirected($this->getTicketByDateRedirected($fromDate), $allDays);
                    $reopenFilteredNoneData         = $this->fillMissingDaysReopen($this->getTicketByDateReopen($fromDate), $allDays);

                } elseif ($dateDiff > 7 && $dateDiff < 30) {
                    // Filter type: month
                    $newFilteredData                = $this->fillMissingWeeks($this->getTicketByDateNew($fromDate), $allWeeks);
                    $resolvedFilteredNoneData       = $this->fillMissingWeeksResolved($this->getTicketByDateResolved($fromDate), $allWeeks);
                    $closedFilteredNoneData         = $this->fillMissingWeeksClosed($this->getTicketByDateClosed($fromDate), $allWeeks);
                    $redirectedFilteredNoneData     = $this->fillMissingWeeksRedirected($this->getTicketByDateRedirected($fromDate), $allWeeks);
                    $reopenFilteredNoneData         = $this->fillMissingWeeksReopen($this->getTicketByDateReopen($fromDate), $allWeeks);
                } else {
                    // Filter type: year
                    $newFilteredData                = $this->fillMissingMonths($this->getTicketByDateNew($fromDate), $allMonths);
                    $resolvedFilteredNoneData       = $this->fillMissingMonthsResolved($this->getTicketByDateResolved($fromDate), $allMonths);
                    $closedFilteredNoneData         = $this->fillMissingMonthsClosed($this->getTicketByDateClosed($fromDate), $allMonths);
                    $redirectedFilteredNoneData     = $this->fillMissingMonthsRedirected($this->getTicketByDateRedirected($fromDate), $allMonths);
                    $reopenFilteredNoneData         = $this->fillMissingMonthsReopen($this->getTicketByDateReopen($fromDate), $allMonths);

                }

                return response()->json(['data' =>
                    [

                            'new'=>$newFilteredData,
                            'resolve'=>$resolvedFilteredNoneData,
                            'close'=>$closedFilteredNoneData,
                            'redirect'=>$redirectedFilteredNoneData,
                            'reopen'=>$reopenFilteredNoneData,

                    ]
                ]);
            }

        }catch (\Exception $e)
        {
            return response()->json(['error' => true , 'message'=>'Something Went Wrong !'],400);
        }

    }

    public function agent()
    {
        $loggedin     = AgentStatusAction::where('agent_status_id','!=',2)->count();
        $available    = AgentStatusAction::where('agent_status_id',3)->count();
        $notAvailable = AgentStatusAction::where('agent_status_id',4)->count();
        $loggedout    = AgentStatusAction::where('agent_status_id',2)->count();

        return response()->json(['data'=>[
            'loggediIn'=>$loggedin,
            'available'=>$available,
            'notAvailable'=>$notAvailable,
            'loggedOut'=>$loggedout,
        ]]);
    }

    private function getTicketByDateNew($date)
    {
        $records = Ticket::whereDate('created_at', $date)

            ->selectRaw('DATE_FORMAT(created_at, "%H") as hour, count(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->pluck('count', 'hour');

        return $records;
    }
    private function getTicketByDateNewRange($startDate, $endDate)
    {
        $records = Ticket::whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate)
            ->selectRaw('DATE_FORMAT(created_at, "%Y-%m-%d") as date, count(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('count', 'date');

        return $records;
    }

    private function getTicketByDateNoneNew($date)
    {
        $records = Ticket::where('status' ,'!=', 'New')->whereDate('created_at', $date)

            ->selectRaw('DATE_FORMAT(created_at, "%H") as hour, count(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->pluck('count', 'hour');

        return $records;
    }
    private function getTicketByDateNoneNewRange($startDate, $endDate)
    {
        $records = Ticket::where('status' ,'!=', 'New')->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate)
            ->selectRaw('DATE_FORMAT(created_at, "%Y-%m-%d") as date, count(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('count', 'date');

        return $records;
    }

    private function getTicketByDateResolved($date)
    {
        $records = Ticket::where('status' , 'Resolved')->whereDate('created_at', $date)

            ->selectRaw('DATE_FORMAT(created_at, "%H") as hour, count(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->pluck('count', 'hour');

        return $records;
    }
    private function getTicketByDateResolvedRange($startDate, $endDate)
    {
        $records = Ticket::where('status', 'Resolved')->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate)
            ->selectRaw('DATE_FORMAT(created_at, "%Y-%m-%d") as date, count(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('count', 'date');

        return $records;
    }

    private function getTicketByDateClosed($date)
    {
        $records = Ticket::where('status' ,'Closed')->whereDate('created_at', $date)

            ->selectRaw('DATE_FORMAT(created_at, "%H") as hour, count(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->pluck('count', 'hour');

        return $records;
    }
    private function getTicketByDateClosedRange($startDate, $endDate)
    {
        $records = Ticket::where('status' ,'Closed')->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate)
            ->selectRaw('DATE_FORMAT(created_at, "%Y-%m-%d") as date, count(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('count', 'date');

        return $records;
    }

    private function getTicketByDateRedirected($date)
    {
        $records = Ticket::where('status' ,'Redirect')->whereDate('created_at', $date)

            ->selectRaw('DATE_FORMAT(created_at, "%H") as hour, count(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->pluck('count', 'hour');

        return $records;
    }
    private function getTicketByDateRedirectedRange($startDate, $endDate)
    {
        $records = Ticket::where('status' ,'Redirect')->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate)
            ->selectRaw('DATE_FORMAT(created_at, "%Y-%m-%d") as date, count(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('count', 'date');

        return $records;
    }

    private function getTicketByDateReopen($date)
    {
        $records = Ticket::where('status' ,'Reopen')->whereDate('created_at', $date)

            ->selectRaw('DATE_FORMAT(created_at, "%H") as hour, count(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->pluck('count', 'hour');

        return $records;
    }
    private function getTicketByDateReopenRange($startDate, $endDate)
    {
        $records = Ticket::where('status' ,'Reopen')->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate)
            ->selectRaw('DATE_FORMAT(created_at, "%Y-%m-%d") as date, count(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('count', 'date');

        return $records;
    }


    private function fillMissingHours($data, $allHours)
    {
        $result = [];

        foreach ($allHours as $hour) {
            $result[$hour] = $data->get($hour, 0);
        }

        return $result;
    }
    private function fillMissingDays($data, $allDays)
    {
        $result = [];

        foreach ($allDays as $day) {
            $dailyData = $this->getTicketByDateNew($day);
            $result[date('D', strtotime($day))] = array_sum($dailyData->toArray());
        }

        return $result;
    }
    private function fillMissingWeeks($data, $allWeeks)
    {
        $result = [];

        foreach ($allWeeks as [$weekStart, $weekEnd]) {
            $weekData = $this->getTicketByDateNewRange($weekStart, $weekEnd);
            $result[date('Y-m-d', strtotime($weekStart))." | $weekEnd"] = array_sum($weekData->toArray());
        }

        return $result;
    }
    private function fillMissingMonths($data, $allMonths)
    {
        $result = [];

        foreach ($allMonths as [$monthStart, $monthEnd]) {
            $monthData = $this->getTicketByDateNewRange($monthStart, $monthEnd);
            $result[date('M', strtotime($monthStart))] = array_sum($monthData->toArray());
        }

        return $result;
    }

    private function fillNoneMissingHours($data, $allHours)
    {
        $result = [];

        foreach ($allHours as $hour) {
            $result[$hour] = $data->get($hour, 0);
        }

        return $result;
    }
    private function fillNoneMissingDays($data, $allDays)
    {
        $result = [];

        foreach ($allDays as $day) {
            $dailyData = $this->getTicketByDateNoneNew($day);
            $result[date('D', strtotime($day))] = array_sum($dailyData->toArray());
        }

        return $result;
    }
    private function fillNoneMissingWeeks($data, $allWeeks)
    {
        $result = [];

        foreach ($allWeeks as [$weekStart, $weekEnd]) {
            $weekData = $this->getTicketByDateNoneNewRange($weekStart, $weekEnd);
            $result[date('Y-m-d', strtotime($weekStart))." | $weekEnd"] = array_sum($weekData->toArray());
        }

        return $result;
    }
    private function fillNoneMissingMonths($data, $allMonths)
    {
        $result = [];

        foreach ($allMonths as [$monthStart, $monthEnd]) {
            $monthData = $this->getTicketByDateNoneNewRange($monthStart, $monthEnd);
            $result[date('M', strtotime($monthStart))] = array_sum($monthData->toArray());
        }

        return $result;
    }


    private function fillMissingHoursResolved($data, $allHours)
    {
        $result = [];

        foreach ($allHours as $hour) {
            $result[$hour] = $data->get($hour, 0);
        }

        return $result;
    }
    private function fillMissingDaysResolved($data, $allDays)
    {
        $result = [];

        foreach ($allDays as $day) {
            $dailyData = $this->getTicketByDateResolved($day);
            $result[date('D', strtotime($day))] = array_sum($dailyData->toArray());
        }

        return $result;
    }
    private function fillMissingWeeksResolved($data, $allWeeks)
    {
        $result = [];

        foreach ($allWeeks as [$weekStart, $weekEnd]) {
            $weekData = $this->getTicketByDateResolvedRange($weekStart, $weekEnd);
            $result[date('Y-m-d', strtotime($weekStart))." | $weekEnd"] = array_sum($weekData->toArray());
        }

        return $result;
    }
    private function fillMissingMonthsResolved($data, $allMonths)
    {
        $result = [];

        foreach ($allMonths as [$monthStart, $monthEnd]) {
            $monthData = $this->getTicketByDateResolvedRange($monthStart, $monthEnd);
            $result[date('M', strtotime($monthStart))] = array_sum($monthData->toArray());
        }

        return $result;
    }


    private function fillMissingHoursClosed($data, $allHours)
    {
        $result = [];

        foreach ($allHours as $hour) {
            $result[$hour] = $data->get($hour, 0);
        }

        return $result;
    }
    private function fillMissingDaysClosed($data, $allDays)
    {
        $result = [];

        foreach ($allDays as $day) {
            $dailyData = $this->getTicketByDateClosed($day);
            $result[date('D', strtotime($day))] = array_sum($dailyData->toArray());
        }

        return $result;
    }
    private function fillMissingWeeksClosed($data, $allWeeks)
    {
        $result = [];

        foreach ($allWeeks as [$weekStart, $weekEnd]) {
            $weekData = $this->getTicketByDateClosedRange($weekStart, $weekEnd);
            $result[date('Y-m-d', strtotime($weekStart))." | $weekEnd"] = array_sum($weekData->toArray());
        }

        return $result;
    }
    private function fillMissingMonthsClosed($data, $allMonths)
    {
        $result = [];

        foreach ($allMonths as [$monthStart, $monthEnd]) {
            $monthData = $this->getTicketByDateClosedRange($monthStart, $monthEnd);
            $result[date('M', strtotime($monthStart))] = array_sum($monthData->toArray());
        }

        return $result;
    }


    private function fillMissingHoursRedirected($data, $allHours)
    {
        $result = [];

        foreach ($allHours as $hour) {
            $result[$hour] = $data->get($hour, 0);
        }

        return $result;
    }
    private function fillMissingDaysRedirected($data, $allDays)
    {
        $result = [];

        foreach ($allDays as $day) {
            $dailyData = $this->getTicketByDateRedirected($day);
            $result[date('D', strtotime($day))] = array_sum($dailyData->toArray());
        }

        return $result;
    }
    private function fillMissingWeeksRedirected($data, $allWeeks)
    {
        $result = [];

        foreach ($allWeeks as [$weekStart, $weekEnd]) {
            $weekData = $this->getTicketByDateRedirectedRange($weekStart, $weekEnd);
            $result[date('Y-m-d', strtotime($weekStart))." | $weekEnd"] = array_sum($weekData->toArray());
        }

        return $result;
    }
    private function fillMissingMonthsRedirected($data, $allMonths)
    {
        $result = [];

        foreach ($allMonths as [$monthStart, $monthEnd]) {
            $monthData = $this->getTicketByDateRedirectedRange($monthStart, $monthEnd);
            $result[date('M', strtotime($monthStart))] = array_sum($monthData->toArray());
        }

        return $result;
    }

    private function fillMissingHoursReopen($data, $allHours)
    {
        $result = [];

        foreach ($allHours as $hour) {
            $result[$hour] = $data->get($hour, 0);
        }

        return $result;
    }
    private function fillMissingDaysReopen($data, $allDays)
    {
        $result = [];

        foreach ($allDays as $day) {
            $dailyData = $this->getTicketByDateReopen($day);
            $result[date('D', strtotime($day))] = array_sum($dailyData->toArray());
        }

        return $result;
    }
    private function fillMissingWeeksReopen($data, $allWeeks)
    {
        $result = [];

        foreach ($allWeeks as [$weekStart, $weekEnd]) {
            $weekData = $this->getTicketByDateReopenRange($weekStart, $weekEnd);
            $result[date('Y-m-d', strtotime($weekStart))." | $weekEnd"] = array_sum($weekData->toArray());
        }

        return $result;
    }
    private function fillMissingMonthsReopen($data, $allMonths)
    {
        $result = [];

        foreach ($allMonths as [$monthStart, $monthEnd]) {
            $monthData = $this->getTicketByDateReopenRange($monthStart, $monthEnd);
            $result[date('M', strtotime($monthStart))] = array_sum($monthData->toArray());
        }

        return $result;
    }

    private function generateAllHours()
    {
        $result = array_map(function ($i) {
            return str_pad($i, 2, '0', STR_PAD_LEFT);
        }, range(0, 23));

        return $result;
    }
    private function generateAllDays($startDate, $endDate)
    {
        $result = [];
        $currentDate = $startDate;

        while ($currentDate <= $endDate) {
            $result[] = $currentDate;
            $currentDate = (new DateTime($currentDate))->modify('+1 day')->format('Y-m-d');
        }

        return $result;
    }
    private function generateAllWeeks($startDate, $endDate)
    {
        $result = [];
        $currentDate = $startDate;

        while ($currentDate <= $endDate) {
            $result[] = [$currentDate, (new DateTime($currentDate))->modify('+6 days')->format('Y-m-d')];
            $currentDate = (new DateTime($currentDate))->modify('+7 days')->format('Y-m-d');
        }

        return $result;
    }
    private function generateAllMonths($startDate, $endDate)
    {
        $result = [];
        $currentDate = $startDate;

        while ($currentDate <= $endDate) {
            $result[] = [$currentDate, (new DateTime($currentDate))->modify('last day of this month')->format('Y-m-d')];
            $currentDate = (new DateTime($currentDate))->modify('first day of next month')->format('Y-m-d');
        }

        return $result;
    }

}
