<?php

namespace App\Http\Controllers\Tenant\Admin\Notifications;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class MainController extends Controller
{
    public function index()
    {
        return view('tenant.admin.notifications.index');
    }
    public function emailSmtp()
    {
        return view('tenant.admin.notifications.emailSmtp');
    }
    public function emailApi()
    {
        return view('tenant.admin.notifications.emailApi');
    }
    public function emailScript()
    {
        return view('tenant.admin.notifications.emailScript');
    }
    public function smsApi()
    {
        return view('tenant.admin.notifications.smsApi');
    }
    public function smsScript()
    {
        return view('tenant.admin.notifications.smsScript');
    }
    public function marketing()
    {
        return view('tenant.admin.notifications.marketing');
    }
}
