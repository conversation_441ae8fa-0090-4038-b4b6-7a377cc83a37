<?php

namespace App\Http\Controllers\Tenant\Admin\Performance;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class PerformanceController extends Controller
{
    public function index()
    {
        return view('tenant.admin.performance.index');
    }

    public function levels()
    {
        return view('tenant.admin.performance.levels');
    }

    public function target()
    {
        return view('tenant.admin.performance.target');
    }

    public function points()
    {
        return view('tenant.admin.performance.points');
    }

    public function earned()
    {
        return view('tenant.admin.performance.earned');
    }
    public function kpispermission()
    {
        return view('tenant.admin.performance.kpispermission');
    }
}
