<?php

namespace App\Console\Commands\Voice;

use App\Models\Tenant;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class FetchMaqsamCalls extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'calls:fetch-maqsam-calls';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch call data from Maqsam API';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $tenants = Tenant::with('domains')->get();

            foreach ($tenants as $tenant)
            {
                foreach ($tenant->domains as $domain)
                {
                    Http::get('https://'.$domain->domain.'/voice/maqsam/calls');
//                  Http::get('https://'.$domain->domain.'/escalation');
                }

            }
        }catch (\Exception $e)
        {

        }
    }
}
