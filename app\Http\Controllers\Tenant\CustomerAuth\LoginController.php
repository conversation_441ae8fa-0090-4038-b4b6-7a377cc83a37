<?php

namespace App\Http\Controllers\Tenant\CustomerAuth;

use App\Events\System\AgentOut;
use App\Http\Controllers\Controller;
use App\Models\Tenant\AgentConfigration;
use App\Models\Tenant\AgentSession;
use App\Models\Tenant\AgentStatusAction;
use App\Models\Tenant\AgentStatusActionLog;
use App\Providers\RouteServiceProvider;
use Carbon\Carbon;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Session;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = RouteServiceProvider::CUSTOMER;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }

    public function redirectTo()
    {
        if (auth()->user()->roles()->first()->allowed_route != '') {

            return $this->redirectTo = '/customer' ;
        }
    }

    protected function authenticated(Request $request, $user)
    {

        if (Cache::has('role_routes')){Cache::forget('role_routes');}

        if (Cache::has('user_routes')){Cache::forget('role_routes');}
    }

    protected function loggedOut(Request $request)
    {

        Cache::forget('role_routes');
        Cache::forget('user_routes');
        return redirect()->route('customer.login') ;
    }

    public function showLoginForm()
    {
        return view('tenant.customer_auth.login');
    }

    public function username()
    {
        return 'email';
    }


}
