<?php

namespace App\Http\Controllers\Api\Supervisor\ContactProfile;

use App\Http\Controllers\Controller;
use App\Models\Tenant\ContactProfile;
use App\Models\Tenant\ContactProfileGroup;
use App\Models\Tenant\ContactProfileSocial;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Spatie\SimpleExcel\SimpleExcelReader;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ContactProfileController extends Controller
{

    public function create(Request $request)
    {
        $data = $request->json()->all();

        if (!is_array($data)) {
            return response()->json(['status' => 'error', 'message' => 'Invalid JSON format'], 422);
        }

        $expectedKeys = [
            'Name',
            'Primary Email',
            'Country Code Number',
            'Primary Phone Number',
            'Profile Image',
            'Attachments',
            'Group Type',
            'Email',
            'Phone',
            'X',
            'Instagram',
            'Facebook',
            'LinkedIn',
            'YouTube',
            'WhatsApp',
            'Snapchat',
            'TikTok',
            'Google',
        ];

        $failedRecords = [];
        $successfulUploads = 0;

        foreach ($data as $index => $row) {
            if (!is_array($row) || empty(array_filter($row))) {
                continue;
            }

            // Ensure all expected keys exist
            foreach ($expectedKeys as $key) {
                if (!array_key_exists($key, $row)) {
                    $row[$key] = null;
                }
            }

            $fullName = $row['Name'];
            $email = $row['Primary Email'];
            $phone = $row['Primary Phone Number'];
            $countryCode = $row['Country Code Number'];
            $groupName = $row['Group Type'];
            $fullPhone = $countryCode . $phone;

            $validator = Validator::make([
                'full_name' => $fullName,
                'primary_email' => $email,
                'customerNumber' => $fullPhone,
                'country_code_number' => $countryCode,
                'contact_profile_group' => $groupName,
            ], [
                'full_name' => 'required|string|max:255',
                'primary_email' => 'required|email|unique:contact_profiles,primary_email',
                'country_code_number' => 'required|numeric|min:1',
                'customerNumber' => ['required', 'regex:/^\+?[0-9]{6,15}$/', 'unique:contact_profiles,primary_phone'],
                'contact_profile_group' => ['required', 'string', 'max:255', 'not_regex:/,/',],
            ]);

            if ($validator->fails()) {
                $failedRecords[] = [
                    'row' => $index + 1,
                    'data' => $row,
                    'errors' => $validator->errors()->all(),
                ];
                continue;
            }

            // Handle Profile Image (base64)
            $profileImagePath = null;
            if (!empty($row['Profile Image'])) {
                $imageData = $row['Profile Image'];
                if (preg_match('/^data:image\/(\w+);base64,/', $imageData, $type)) {
                    $imageData = substr($imageData, strpos($imageData, ',') + 1);
                    $imageData = base64_decode($imageData);
                    $extension = strtolower($type[1]);
                    $filename = md5($row['Primary Email'] . microtime()) . '.' . $extension;
                    Storage::disk('public')->put("contact_profile/{$filename}", $imageData);
                    $profileImagePath = $filename;
                }
            }

            $group = ContactProfileGroup::firstOrCreate(['name' => $groupName]);

            $contactProfile = ContactProfile::create([
                'user_id' => auth()->id(),
                'full_name' => encrypt($fullName),
                'primary_email' => $email,
                'primary_phone' => $fullPhone,
                'contact_profile_group_id' => $group->id,
                'contact_profile_image' => $profileImagePath,
            ]);

            // Social Media
            $socialKeys = [
                'Email', 'Phone', 'X', 'Instagram', 'Facebook',
                'LinkedIn', 'YouTube', 'WhatsApp', 'Snapchat', 'TikTok', 'Google'
            ];

            foreach ($socialKeys as $platformKey) {
                $value = $row[$platformKey] ?? null;
                if ($value) {
                    ContactProfileSocial::create([
                        'contact_profile_id' => $contactProfile->id,
                        'platform' => $platformKey,
                        'value' => $value,
                        'icon' => $platformKey,
                    ]);
                }
            }

            // Handle Attachments (array of base64 strings)
            if (!empty($row['Attachments']) && is_array($row['Attachments'])) {
                foreach ($row['Attachments'] as $attachmentBase64) {
                    if (preg_match('/^data:(.*);base64,/', $attachmentBase64, $match)) {
                        $extension = explode('/', $match[1])[1] ?? 'bin';
                        $attachmentData = base64_decode(substr($attachmentBase64, strpos($attachmentBase64, ',') + 1));
                        $attachmentName = Str::uuid() . '.' . $extension;

                        Storage::disk('public')->put("contract_attachment/{$attachmentName}", $attachmentData);

                        $contactProfile->customerContractAttachment()->create([
                            'contact_profile_id' => $contactProfile->id,
                            'name' => $attachmentName,
                        ]);
                    }
                }
            }

            $successfulUploads++;
        }

        return response()->json([
            'status' => 'success',
            'uploaded' => $successfulUploads,
            'failed' => count($failedRecords),
            'errors' => $failedRecords,
        ]);
    }

    public function upload1(Request $request)
    {

        // dd($file);
        $failedRecords = [];
        $totalRows = 0;
        $successfulUploads = 0;
        $filePath = $file->getRealPath();
        $reader = SimpleExcelReader::create($filePath);
        $rows = $reader->getRows();
        $actualHeaders = $reader->getHeaders();

        $expectedHeaders = [
            'Name',
            'Primary Email',
            'Country Code Number',
            'Primary Phone Number',
            'Group Type',
            'Email',
            'Phone',
            'X',
            'Instagram',
            'Facebook',
            'LinkedIn',
            'YouTube',
            'WhatsApp',
            'Snapchat',
            'TikTok',
            'Google',
        ];

        // Compare headers strictly (including order)
        if (array_values($actualHeaders) !== array_values($expectedHeaders)) {
            $this->alert('error', 'Header mismatch!', [
                'text' => 'Expected headers do not match uploaded file headers.',
                'toast' => false,
                'position' => 'center',
                'timer' => 8000,
                'timerProgressBar' => true,
                'showCloseButton' => true,
                'showConfirmButton' => true,
                'confirmButtonText' => 'Close',
            ]);
            return;
        }
        $failedRecords = [];
        $totalRows = 0;
        foreach ($rows as $index => $row) {
            if (empty(array_filter($row))) {
                continue;
            }

            $totalRows++;

            // Ensure all columns are present
            $row = array_pad($row, count($expectedHeaders), null);

            $fullName = $row['Name'] ?? null;
            $email = $row['Primary Email'] ?? null;
            $phone = $row['Primary Phone Number'] ?? null;
            $country_code_number = $row['Country Code Number'] ?? null;
            $contact_profile_group = $row['Group Type'] ?? null;
            $phone = $country_code_number . $phone;

            $validator = Validator::make([
                'full_name' => $fullName,
                'primary_email' => $email,
                'customerNumber' => $phone,
                'country_code_number' => $country_code_number,
                'contact_profile_group' => $contact_profile_group,
            ], [
                'full_name' => 'required|string|max:255',
                'primary_email' => 'required|email|unique:contact_profiles,primary_email',
                'country_code_number' => 'required|numeric|min:1',
                'customerNumber' => [
                    'required',
                    'regex:/^\+?[0-9]{6,15}$/',
                    'unique:contact_profiles,primary_phone',
                ],
                'contact_profile_group' => [
                    'required',
                    'string',
                    'max:255',
                    'not_regex:/,/', // Does not allow comma
                ],
            ]);

            if ($validator->fails()) {
                $failedRecords[] = [
                    'row' => $index + 1,
                    'data' => $row,
                    'errors' => $validator->errors()->all(),
                ];
                continue;
            }
            // $phone = $country_code_number . $phone;
            $data = [
                'user_id' => auth()->id(),
                'full_name' => encrypt($fullName),
                'primary_email' => $email,
                'primary_phone' => $phone,
            ];

            $group = ContactProfileGroup::firstOrCreate(['name' => $contact_profile_group]);
            $data['contact_profile_group_id'] = $group->id;

            $contactProfile = ContactProfile::create($data);
            $successfulUploads++;

            $socialKeys = array_slice($expectedHeaders, 4);
            foreach ($socialKeys as $platformKey) {
                $value = $row[$platformKey] ?? null;

                if ($value) {
                    ContactProfileSocial::create([
                        'contact_profile_id' => $contactProfile->id,
                        'platform' => $platformKey,
                        'value' => $value,
                        'icon' => $platformKey,
                    ]);
                }
            }
        }

    }
}
