<?php

namespace App\Http\Controllers\Tenant\Agent\Main;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Resource;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use App\Models\Tenant\PlatformSla;



class DashboardController extends Controller
{
    public function getReopen()
    {

        $query = DB::table('resources')
            ->select('*')
            ->join('resource_infos', 'resources.id', '=', 'resource_infos.resource_id')
            ->whereDate('resources.created_at', '=', date('Y-m-d'))
            ->where('resources.status', 'Reopen');


        return $count = $query->count();

    }

    public function __invoke(){


        $resourceActions = DB::table('resource_actions')
            ->select('resource_id', DB::raw('TIMESTAMPDIFF(SECOND, MIN(created_at), MAX(created_at)) AS time_difference'))
            ->groupBy('resource_id')
            ->get();

        $totalTime = 0;
        $count = 0;

        foreach ($resourceActions as $resourceAction) {
            $timeDifferenceInSeconds = $resourceAction->time_difference;
            $totalTime += $timeDifferenceInSeconds;
            $count++;
        }

        $averageTimeInSeconds = $count > 0 ? $totalTime / $count : 0;
        $averageTimeInHours = $averageTimeInSeconds / 3600;

        $averageTime = Carbon::now()->startOfDay()->addSeconds($averageTimeInHours);
        $averageHour = $averageTime->format('H');





        $All  = Resource::count();
        $All2 = Resource::where('status','New')->count();
        $All3 = Resource::where('status','Pending')->count();

        if ($All != 0 && $All2 !=0)
        {
            $art = ($All2/$All) *100/100;
            $rws = ($All/$All2)/15;
            $asds = $All3;
        }else
        {
            $art = 0;
            $rws = 0;
            $asds = 0;
        }



        $today = Carbon::today();

        $counts = Resource::selectRaw('HOUR(created_at) as hour, COUNT(*) as count')
            ->whereDate('created_at', $today)
            ->groupBy('hour')
            ->pluck('count', 'hour');

        $result = [];

        for ($hour = 0; $hour <= 23; $hour++) {
            $count = $counts[$hour] ?? 0;
            $result[$hour] = $count;
        }




        $yes = Carbon::yesterday();

        $counts2 = Resource::selectRaw('HOUR(created_at) as hour, COUNT(*) as count')
            ->whereDate('created_at', $yes)
            ->groupBy('hour')
            ->pluck('count', 'hour');

        $result2 = [];

        for ($hour = 0; $hour <= 23; $hour++) {
            $count2 = $counts2[$hour] ?? 0;
            $result2[$hour] = $count2;
        }



        return view('tenant.agent.main.index',compact('art','rws','asds','result','result2','averageHour'));
    }

}
