<?php
namespace App\Exports\Reports\Tagging;

use App\Models\Tenant\CategoryLevel;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Illuminate\Support\Collection;

class TaggingExport implements FromCollection, WithHeadings, WithMapping
{
    public $tagging;
    public $levels;

    public function __construct($tagging)
    {
        $this->tagging = $tagging;
        $this->levels = CategoryLevel::whereStatus(1)->get();
    }

    public function collection(): Collection
    {
        return collect($this->tagging);
    }

    public function headings(): array
    {
        $base = ['Ticket ID', 'Channel', 'Type', 'Customer Name'];
        $dynamic = $this->levels->pluck('label')->toArray();
        return array_merge($base, $dynamic, ['Date']);
    }

    public function map($ticket): array
    {
        $row = [];

        $row[] = $ticket->id;
        $row[] = $ticket->resourceInfo->channel ?? '-';
        $row[] = $ticket->resourceInfo->type ?? '-';
        $row[] = optional(optional($ticket->resourceInfo)->customer)->display_name ?? '-';

        // Fetch last action
        $lastAction = $ticket->resourceCategories()
            ->orderBy('created_at', 'desc')
            ->get()
            ->groupBy(function ($item) {
                return $item->created_at;
            })
            ->first();

        if ($lastAction) {
            foreach ($lastAction as $cat) {
                $row[] = $cat->category->name ?? '-';
            }
            // Fill remaining cells if less than levels
            for ($i = count($lastAction); $i < count($this->levels); $i++) {
                $row[] = '-';
            }
        } else {
            foreach ($this->levels as $level) {
                $row[] = '-';
            }
        }

        $row[] = $ticket->created_at ? $ticket->created_at->format('Y-m-d H:i:s') : '-';

        return $row;
    }
}
