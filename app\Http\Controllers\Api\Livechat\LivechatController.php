<?php

namespace App\Http\Controllers\Api\Livechat;

use HTMLPurifier;
use HTMLPurifier_Config;
use Illuminate\Http\Request;
use App\Traits\AssignToAgent;
use App\Models\Tenant\LiveChat;
use App\Models\Tenant\Resource;
use App\Models\Tenant\AgentQueue;
use App\Models\Tenant\BotCustomer;
use App\Models\Tenant\BotResource;
use Illuminate\Support\Facades\DB;
use App\Models\Tenant\BotBootpress;
use App\Models\Tenant\ResourceInfo;
use App\Models\Tenant\SourceRating;
use App\Http\Controllers\Controller;
use App\Models\Tenant\LiveChatMessage;
use Illuminate\Support\Facades\Storage;
use App\Events\Webhook\Livechat\EndChat;
use Illuminate\Support\Facades\Validator;
use App\Events\Webhook\Livechat\ChatNotify;
use App\Models\Tenant\LivechatConfiguration;
use App\Http\Resources\Livechat\StartResource;
use App\Models\Tenant\BotBootpressMessage;

class LivechatController extends Controller
{
    use AssignToAgent;

    public function store(Request $request)
    {

        $validation = Validator::make($request->all(), [
            'username'      => 'required',
            'ip_address'    => 'required',
            'user_session'  => 'required',
        ]);

        if ($validation->fails()) {
            return response()->json(['errors' => true, 'message' => $validation->errors()], 200);
        }

        $get_agent = self::getUserId('Livechat', ['English', 'Arabic']);

        if ($get_agent != 0) {
            AgentQueue::where('user_id', $get_agent)
                ->where('platform', 'Livechat')
                ->increment('queue');
        }

        $resourceTable = resource::create([
            'status' => 'New',
            'user_id' => $get_agent
        ]);

        $resourceInfoTable = $resourceTable->resourceInfo()->create([
            'channel' => 'Livechat',
            'type' => 'Message',
            'account_name' => $request->ip_address,
            'content' => 'New Chat',
        ]);
        $resourceInfoTable->customer()->create([
            'username'      =>  $request->username,
            'display_name'  =>  $request->username,
            'profile_img' => 'avatar.png',
        ]);


        $data['username'] = $request->username;
        $data['ip_address'] = $request->ip_address;
        $data['session']    = $request->user_session;
        $data['url_source'] = $request->url();

        $liveChatTable = $resourceInfoTable->livechat()->create($data);

        $resourceTable->resourceActions()->create(['action' => 'New', 'user_id' => 0, 'comment' => 'New Livechat Message']);

        $message = $liveChatTable->messages()->create([
            'type' => 'Inbound',
            'attachment' => null,
            'mime_type' => null,
            'message' => 'New Chat',
        ]);


        $data_hook['user_id'] = $get_agent;
        $data_hook['icon'] = 'fa-rocketchat';
        $data_hook['resource_id'] = $resourceTable->id;
        $data_hook['resource_info_id'] = $resourceInfoTable->id;
        $data_hook['livechat_id'] = $liveChatTable->id;
        $data_hook['message_id'] = $message->id;
        $data_hook['name'] = $liveChatTable->username;
        $data_hook['content'] = $message->message;


        broadcast(new ChatNotify(tenant('id'), $data_hook));

        $resource = Resource::whereId($resourceTable->id)->first();

        return new StartResource($resource);
    }

    public function getUserWhereToken(Request $request)
    {

        $validation = Validator::make($request->all(), [
            'user_session'  => 'required',
        ]);

        if ($validation->fails()) {
            return response()->json(['errors' => true, 'message' => $validation->errors()], 200);
        }

        $chat = BotBootpress::where('username', $request->user_session)->first();

        if ($chat) {
            return response()->json(['Username' => $chat->name], 200);
        }

        return response()->json(['errors' => true, 'message' => []], 200);
    }

    public function message(Request $request)
    {

        $validation = Validator::make($request->all(), [
            'sourceId'     => 'required',
            'message'     => 'required_if:attachment,0',
            // 'attachment'   => 'required_without:message',
            'attachment'   => 'required_if:message,0',
        ]);

        if ($validation->fails()) {
            return response()->json(['errors' => true, 'message' => $validation->errors()], 200);
        }

        $msg = "attachment";

        if (isset($request->message)) {
            $msg = $request->message;

            $config = HTMLPurifier_Config::createDefault();
            $purifier = new HTMLPurifier($config);

            $msg = $purifier->purify($msg);
        }

        $resource = Resource::whereId($request->sourceId)->first();

        if ($resource) {
            if ($resource->status != 'New') {
                return response()->json(['errors' => true, 'message' => 'Message Has Been Ended'], 200);
            }

            $resource->resourceInfo->update(['read_at' => null, 'content' => $msg]);

            if ($attachmentData = $request->file('attachment')) {

                $filename = rand(1, 100000) . strtotime(now()) . '.' . $attachmentData->getClientOriginalExtension();

                $attachment = $filename;
                // Get the MIME type of the uploaded file
                $mime_type = $attachmentData->getClientMimeType();

                Storage::disk('public')->put('/livechat/' . $filename, file_get_contents($attachmentData));
            } else {
                $attachment = null;
                $mime_type = null;
            }

            $message  = $resource->resourceInfo->livechat->messages()->create([
                'type' => 'Inbound',
                'attachment' => $attachment,
                'mime_type' => $mime_type,
                'message' => $msg,
            ]);

            $data_hook['user_id'] = $resource->user_id;
            $data_hook['icon'] = 'fa-rocketchat';
            $data_hook['resource_id'] = $resource->id;
            $data_hook['resource_info_id'] = $resource->resourceInfo->id;
            $data_hook['livechat_id'] = $resource->resourceInfo->livechat->id;
            $data_hook['message_id'] = $message->id;
            $data_hook['name'] = $resource->resourceInfo->livechat->username;
            $data_hook['content'] = $msg;


            broadcast(new ChatNotify(tenant('id'), $data_hook));


            return response()->json(['errors' => false, 'message' => 'Message Sent Successfully'], 200);
        } else {
            return response()->json(['errors' => true, 'message' => 'No sourceId Found'], 200);
        }
    }

    public function end(Request $request)
    {
        $validation = Validator::make($request->all(), [
            'user_session'  => 'required_without:username',
            'username'  => 'required_without:user_session',
        ]);

        if ($validation->fails()) {
            return response()->json(['errors' => true, 'message' => $validation->errors()], 200);
        }

        $chat   = BotBootpress::where(function ($q) use ($request) {
            $q->where('name', $request->username)
                ->orWhere('username', $request->user_session);
        })->orderBy('id', 'desc')->first();
        if ($chat) {
            broadcast(new EndChat(tenant('id'), $chat->username));
            return response()->json(['errors' => true, 'message' => 'Ended Successfully'], 200);
            //return response()->json(['errors' => true, 'message' => $chat], 200);
        }

        $chat2  = LiveChat::where(function ($q) use ($request) {
            $q->where('username', $request->username)
                ->orWhere('session', $request->user_session);
        })->orderBy('id', 'desc')->first();

        if ($chat2) {
            broadcast(new EndChat(tenant('id'), $chat2->session));
            return response()->json(['errors' => true, 'message' => 'Ended Successfully'], 200);
            //return response()->json(['errors' => true, 'message' => $chat2], 200);
        }
        return response()->json(['errors' => true, 'message' => 'No User Found'], 200);
    }

    public function storeBotRate(Request $request)
    {
        try {
            $validation = Validator::make($request->all(), [
                'rateValue'        => 'required',
                'rateTitle'        => 'required',
                'comment'          => 'nullable',
                'botSession'       => 'required',

            ]);

            if ($validation->fails()) {
                return response()->json(['errors' => true, 'message' => $validation->errors()], 200);
            }

            $botBootpress = BotBootpress::where('Username', $request->botSession)->first();

            if ($botBootpress) {
                $data['rate_value'] = $request->rateValue;
                $data['comment'] = $request->comment;
                $data['rate_title'] = $request->rateTitle;
                $data['user_id'] = null;
                $data['bot_id'] = $botBootpress->account_id;
                $data['resource_id'] = null;
                $data['bot_resource_id'] = $botBootpress->botResourceInfo->bot_resource_id;


                $rating = SourceRating::create($data);

                if ($rating) {
                    return response()->json(['message' => 'successfully rating'], 200);
                } else {
                    return response()->json(['errors' => true, 'message' => 'Something was wrong!'], 200);
                }
            } else {
                return response()->json(['errors' => true, 'message' => 'No Session Found'], 200);
            }
        } catch (\Exception $e) {
            return response()->json(['errors' => true, 'message' => 'Something was wrong!'], 400);
        }
    }

    public function getMessages(Request $request)
    {
        $validation = Validator::make($request->all(), [
            // 'sourceId'     => 'required',
            'userSession'     => 'required',
        ]);

        if ($validation->fails()) {
            return response()->json(['errors' => true, 'message' => $validation->errors()], 200);
        };

        // $source_id = $request->sourceId;
        $session_id = $request->userSession;

        $messages = Livechat::with('messages')->where('session', $session_id)?->first()?->messages;

        // $messages = LiveChatMessage::select('message', 'created_at')->whereBelongsTo('liveChat', function ($q) use ($session_id, $source_id) {
        //     $q->where('session', $session_id)->whereHas('resourceInfo', function ($q) use ($source_id) {
        //             $q->where('resource_id', $source_id);
        //         });
        // })->get();
        return response()->json([
            'errors' => false,
            'messages' => $messages
        ], 200);
    }

    public function getConfig()
    {
        // fetch the configuration 
        $config = LivechatConfiguration::all();

        return response()->json(['config' => $config]);
    }

    public function storeBot(Request $request)
    {
        // $validation = Validator::make($request->all(), [
        //     'username'      => 'required',
        //     'ip_address'    => 'required',
        //     'session'  => 'required',
        //     'bot_id'  => 'required',
        // ]);

        $resourceTable = BotResource::create([
            'status' => 1,
            'bot_id' => $request->bot_id
        ]);

        $resourceInfoTable = $resourceTable->botResourceInfo()->create([
            'channel' => 'Livechat',
            'type' => 'Message',
            'account_name' => $request->ip_address,
            'content' => 'New Chat',
            'bot_resource_id' => $resourceTable->id
        ]);

        BotCustomer::create([
            'username'      =>  $request->username,
            'display_name'  =>  $request->username,
            'bot_resource_id' => $resourceTable->id,
        ]);
        // $customerTable = $resourceInfoTable->botCustomer()->create([
        //     'username'      =>  $request->username,
        //     'display_name'  =>  $request->username,
        //     'bot_resource_id' => $resourceTable->id,
        // ]);

        // $data['account_id'] = $request->account_id;
        // $data['username'] = $request->session;
        // $data['user_id'] = $request->ip_address;
        // $data['name']    = $request->username;
        // $data['type'] = 'text';
        // $data['bot_resource_info_id'] = $resourceInfoTable->id;

        // $liveChatTable = $resourceInfoTable->bootpress()->create($data);

        $bootpressId = DB::table('bot_bootpresses')->insertGetId([
            'account_id' => $request->account_id,
            'username' => $request->session,
            'user_id' => $request->ip_address,
            'name' => $request->username,
            'type' => 'text',
            'bot_resource_info_id' => $resourceInfoTable->id,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        return response()->json([
            'errors' => false,
            'bootpress_id' => $bootpressId
        ]);
    }

    public function botGetMessages(Request $request)
    {
        $validation = Validator::make($request->all(), [
            'session'      => 'required',
        ]);

        if ($validation->fails()) {
            return response()->json(['errors' => true, 'message' => $validation->errors()], 200);
        }

        $messages = BotBootpress::where('username', $request->session)->first()?->messages()->orderBy('created_at', 'asc')->get();

        if ($messages) {
            return response()->json([
                'errors' => false,
                'messages' => $messages,
            ], 200);
        } else {
            return response()->json([
                'errors' => true,
                'message' => 'Invalid Session'
            ], 400);
        }
    }

    public function storeBotAndUserMessages(Request $request)
    {
        $validation = Validator::make($request->all(), [
            'session' => 'required',
            'message' => 'required|string',
            'type' => 'required|string',
        ]);

        if ($validation->fails()) {
            return response()->json(['errors' => true, 'message' => $validation->errors()], 200);
        }

        // Check if the Bootpress exists
        $bootpress = BotBootpress::where('username', $request->session)->first();

        if (!$bootpress) {
            return response()->json(['errors' => true, 'message' => 'Bootpress not found'], 404);
        }

        BotBootpressMessage::create([
            'type' => $request->type,
            'message' => $request->message,
            'bot_bootpress_id' => $bootpress->id,
        ]);

        return response()->json(['success' => true, 'message' => 'Message saved successfully'], 200);
    }
}
