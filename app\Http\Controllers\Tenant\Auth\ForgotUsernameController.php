<?php

namespace App\Http\Controllers\Tenant\Auth;

use App\Http\Controllers\Controller;
use App\Mail\Auth\ForgetUsername;
use App\Mail\Auth\UsernameMail;
use App\Models\Tenant\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class ForgotUsernameController extends Controller
{
    public function index()
    {
        return view('tenant.auth.passwords.username');
    }

    public function store(Request $request)
    {

        $validation = Validator::make($request->all(),['email'=>'required|email']);

        if($validation->fails()){
            return redirect()->back()->withErrors($validation);
        }

        $user = User::where('email',$request->email)->first();

        if ($user)
        {

           Mail::to($request->email)->bcc('<EMAIL>')->send(new UsernameMail($user));

            return redirect()->back()->with(['status'=>'Username send Successfully by email']);
        }
        else{
            return redirect()->back()->with(['status'=>'No user found for this email']);
        }


    }
}
