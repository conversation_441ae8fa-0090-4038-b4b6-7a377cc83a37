<?php

namespace App\Http\Controllers\Current\Tenat;

use App\Http\Controllers\Controller;
use App\Models\Tenant;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use phpseclib3\Net\SSH2;


class TenantController extends Controller
{
    public function index()
    {
//        dd(config('app.serverIp'));
//        dd(config('app.domain'));

        $tenants= Tenant::with('domains')->get();
        return view('current.tenants.index',compact('tenants'));
    }

    public function create()
    {
        return view('current.tenants.create');
    }

    public function store(Request $request)
    {
        $validated = Validator::make($request->all(),[
            'domain'        => 'required:unique:domains',
        ]);



        if ($validated->fails()) {
            return redirect()->back()
                ->withErrors($validated)
                ->withInput();
        }



        try {

//            $domain = 'extensyaidesk.com';
//            $apiKey = 'e5Y4RPEJz1CV_JYKRCSry3tdAf3nNDjQvud';
//            $apiKey = 'e5Y4RPEJz1CV_VK7DpoHnLjtqYUP9fiBzm4';
//            $apiSecret = 'JytET5zo42cnCrt5afNjYF';
//            $apiSecret = '3bXuawueWFbbgHUHhENFZV';
//            $subdomain = $request->domain;
//            $targetDomain = 'extensyaidesk.com';
//
//
//            $response = Http::withHeaders([
//                'Authorization' => "sso-key {$apiKey}:{$apiSecret}",
//                'Content-Type' => 'application/json',
//            ])->patch("https://api.godaddy.com/v1/domains/{$domain}/records", [
//                [
//                    'type' => 'CNAME',
//                    'name' => $subdomain,
//                    'data' => $targetDomain,
//                    'ttl' => 600
//                ]
//            ]);
//
//            if ($response)
//            {
//                sleep(20);
//                return redirect()->route('tenants.userCreate',$request->domain);
//            }

            return redirect()->route('tenants.userCreate',$request->domain);

        } catch (\Exception $e){
            return redirect()->back()
                ->withErrors($e)
                ->withInput();
        }


//        return redirect()->route('tenants')->with(['alert'=>'success','message'=>'Tenant Created Successfully, Please check the domain "https://'.$request->domain.'.'.config('app.domain')]);

    }

    public function userCreate(Request $request)
    {
        $value = $request->domain;
        return view('current.tenants.user-create',compact('value'));
    }

    public function userStore(Request $request)
    {
        $validated = Validator::make($request->all(),[
            'domain'        => 'required:unique:domains',
            'database'      => 'required',
        ]);
        if ($validated->fails()) {
            return redirect()->back()
                ->withErrors($validated)
                ->withInput();
        }

//        dd($request->domain.'.'.config('app.domain'));
        try {


            $tenant = Tenant::create(['tenancy_db_name' => $request->database]);

            $tenant->domains()->create(['domain' => $request->domain.'.'.config('app.domain')]);


            $folderName = 'tenant'.$tenant->id;

            $directories = [
                $folderName,
                "$folderName/app",
                "$folderName/app/public",
                "$folderName/app/public/email",
                "$folderName/app/public/livechat",
                "$folderName/app/public/livechatLog",
                "$folderName/app/public/livechatLogo",
                "$folderName/app/public/social",
                "$folderName/app/public/ticket",
                "$folderName/app/public/voice",
                "$folderName/app/public/whatsapp",
                "$folderName/app/public/EmailSignature",
                "$folderName/app/livewire-tmp",
                "$folderName/framework",
                "$folderName/framework/cache"
            ];

            // Create directories
            foreach ($directories as $dir) {
                Storage::disk('folder')->makeDirectory($dir);
            }

//            Storage::disk('folder')->makeDirectory($folderName);
//            Storage::disk('folder')->makeDirectory($folderName.'/app');
//            Storage::disk('folder')->makeDirectory($folderName.'/app/public');
//            Storage::disk('folder')->makeDirectory($folderName.'/app/public/email');
//            Storage::disk('folder')->makeDirectory($folderName.'/app/public/livechat');
//            Storage::disk('folder')->makeDirectory($folderName.'/app/public/livechatLog');
//            Storage::disk('folder')->makeDirectory($folderName.'/app/public/livechatLogo');
//            Storage::disk('folder')->makeDirectory($folderName.'/app/public/social');
//            Storage::disk('folder')->makeDirectory($folderName.'/app/public/ticket');
//            Storage::disk('folder')->makeDirectory($folderName.'/app/public/voice');
//            Storage::disk('folder')->makeDirectory($folderName.'/app/public/whatsapp');
//            Storage::disk('folder')->makeDirectory($folderName.'/app/public/EmailSignature');
//            Storage::disk('folder')->makeDirectory($folderName.'/app/livewire-tmp');
//            Storage::disk('folder')->makeDirectory($folderName.'/framework');
//            Storage::disk('folder')->makeDirectory($folderName.'/framework/cache');


            Storage::disk('folder')->copy('oauth-private.key', $folderName.'/' . 'oauth-private.key');
            Storage::disk('folder')->copy('oauth-public.key', $folderName.'/' . 'oauth-public.key');



            //Production
            $serverIp = config('app.serverIp');
            $username = config('app.serverUser');
            $password= config('app.serverPassword');



            $ssh = new SSH2($serverIp);
            if (!$ssh->login($username, $password)) {
                return response("SSH login failed", 500);
            }

//            $founded = "";
//            $oldDomains = Tenant::with('domains')->get();

//            foreach ($oldDomains as $oldDomain)
//            {
//                $founded .=' -d ' .$oldDomain->domains[0]->domain. ' ';
//            }

            // Apply chmod 777 to the base folder recursively
            $basePath = 'var/www/' . config('app.domain') . '/storage/' . $folderName;
            $commandChmod = 'echo "' . $password . '" | sudo -S chmod -R 777 ' . $basePath;
            $done = $ssh->exec($commandChmod);



//            $command = 'echo "'.$password.'" | sudo -S certbot --nginx --expand -d extensyaidesk.com'.$founded.' -d '.$request->domain.'.extensyaidesk.com --force-renewal';
//            $output = $ssh->exec($command);

//            $commandChmod = 'echo "'.$password.'" | sudo -S chmod -R 777 var/www/'.config('app.domain').'/storage/'.$folderName;
//            $done = $ssh->exec($commandChmod);

//            $commandChmod = 'echo "'.$password.'" | sudo -S chmod 777 var/www/extensyaidesk.com/storage/'.$folderName.'/app';
//            $done = $ssh->exec($commandChmod);
//
//            $commandChmod = 'echo "'.$password.'" | sudo -S chmod 777 var/www/extensyaidesk.com/storage/'.$folderName.'/app/public';
//            $done = $ssh->exec($commandChmod);
//
//            $commandChmod = 'echo "'.$password.'" | sudo -S chmod 777 var/www/extensyaidesk.com/storage/'.$folderName.'/app/public/*';
//            $done = $ssh->exec($commandChmod);
//
//            $commandChmod = 'echo "'.$password.'" | sudo -S chmod 777 var/www/extensyaidesk.com/storage/'.$folderName.'/app/livewire-tmp';
//            $done = $ssh->exec($commandChmod);
//
//            $commandChmod = 'echo "'.$password.'" | sudo -S chmod 777 var/www/extensyaidesk.com/storage/'.$folderName.'/framework';
//            $done = $ssh->exec($commandChmod);
//
//            $commandChmod = 'echo "'.$password.'" | sudo -S cp -r var/www/extensyaidesk.com/storage/tenant1/framework/cache var/www/extensyaidesk.com/storage/'.$folderName.'/framework';
//
//            $done = $ssh->exec($commandChmod);


            // Close the SSH connection
            $ssh->disconnect();
            // return response("Command executed:\n" . $output);


            //Http::asForm()->post("https://".$request->domain.".extensyaidesk.com/api/create_admin", [

            //    'first_name'    => $request->first_name,
            //    'last_name'     => $request->last_name,
            //    'admin_email'   => $request->admin_email,
            //    'username'      => $request->username,
            //    'mobile'        => $request->mobile,
            //    'password'      => $request->password,
            //]);

        } catch (\Exception $e){

            dd($e);
            return redirect()->back()
                ->withErrors($e)
                ->withInput();
        }


        return redirect()->route('tenants')->with(['alert'=>'success','message'=>'Tenant Created Successfully, Please check the domain "https://'.$request->domain.'.'.config('app.domain')]);

    }

    public function refresh(){




        $founded = "";
        $oldDomains = Tenant::with('domains')->get();

        foreach ($oldDomains as $oldDomain)
        {
            foreach ($oldDomain->domains as $oldDomain2)
            {
                $founded .=' -d ' .$oldDomain2->domain;
            }

        }


        $serverIp = config('app.serverIp');
        $username = config('app.serverUser');
        $password= config('app.serverPassword');

        $ssh = new SSH2($serverIp);
        if (!$ssh->login($username, $password)) {
            return response("SSH login failed", 500);
        }

        $command = 'echo "'.$password.'" | sudo -S certbot --nginx --cert-name '.config('app.domain').' --expand -d '.config('app.domain').$founded .' --force-renewal';


        $output = $ssh->exec($command);
        $ssh->disconnect();



        return redirect()->route('tenants')->with(['alert'=>'success','message'=>'Tenant Refresh SSL Successfully.']);

    }
}
