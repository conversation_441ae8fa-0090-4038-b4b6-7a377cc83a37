<?php

namespace App\Http\Controllers\Tenant\Admin\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Department;
use App\Models\Tenant\Role;
use App\Models\Tenant\User;

class DashboardController extends Controller
{

    public function index()
    {
        $agent = User::query()->with('agentRoles')->whereHas('roles',function ($q){$q->where('allowed_route','agent');})->count();
        $supervisor = User::query()->with('agentRoles')->whereHas('roles',function ($q){$q->where('allowed_route','supervisor');})->count();
        $client = User::query()->with('agentRoles')->whereHas('roles',function ($q){$q->where('allowed_route','client');})->count();
        $admin = User::query()->with('agentRoles')->whereHas('roles',function ($q){$q->where('allowed_route','admin');})->count();
        $roles = Role::query()->count();
        $departments = Department::query()->count();

//        dd($departments);


        return view('tenant.admin.dashboards.index',compact('agent','supervisor','roles','client','departments','admin'));
    }

}
