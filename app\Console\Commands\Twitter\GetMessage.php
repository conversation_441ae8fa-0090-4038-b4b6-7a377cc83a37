<?php

namespace App\Console\Commands\Twitter;

use App\Models\Tenant;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class GetMessage extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'twitter:messages';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            // $tenants = Tenant::with('domains')->get();

            // foreach ($tenants as $tenant) {
            //     foreach ($tenant->domains as $domain) {
            //         Http::get('https://' . $domain->domain . '/webhook/twitter_dm');
            //     }
            // }
//            Log::info("get_twitter_messages command Success");
            $tenants = Tenant::with('domains')->get();

            foreach ($tenants as $tenant) {
                foreach ($tenant->domains as $domain) {
                    try {
                        Http::withOptions(['verify' => false])->get('http://' . $domain->domain . '/webhook/twitter_dm');
//                        Log::info('Successfully fetched twitter messages for domain', ['domain' => $domain->domain]);
                    } catch (\Exception $e) {
                        Log::error('Failed to fetch twitter messages for domain', [
                            'domain' => $domain->domain,
                            'message' => $e->getMessage(),
                        ]);
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error('get_twitter_messages command failed', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }
}
