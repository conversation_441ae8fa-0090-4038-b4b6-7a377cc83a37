<?php

namespace App\Exports\Reports\Users;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class PerStatusExport implements FromCollection, WithHeadings, WithMapping, ShouldAutoSize
{
    protected $perStatusData;

    public function __construct($perStatusData)
    {
        $this->perStatusData = $perStatusData;
    }

    public function collection()
    {
        // Handle flat array structure from index.php
        return collect($this->perStatusData);
    }

    public function map($row): array
    {
        return [
            $row['user_id'] ?? '',
            $row['name'] ?? '',
            $row['status'] ?? '',
            $row['from'] ?? '',
            $row['to'] ?? '',
            $row['total_staff_time'] ?? '',
        ];
    }

    public function headings(): array
    {
        return [
            'User ID',
            'Name',
            'Status',
            'From',
            'To',
            'Total Staff Time',
        ];
    }
}