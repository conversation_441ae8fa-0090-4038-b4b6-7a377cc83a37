<?php

namespace App\Http\Controllers\Current\Main;

use App\Http\Controllers\Controller;
use App\Models\Tenant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;

class DashboardController extends Controller
{

    public function index()
    {

        $tenants = Tenant::all();
        $tenants_latest = Tenant::orderBy('created_at','desc')->limit(6)->get();

//dd($tenants_latest);
        $tenant_count = count($tenants);


        return view('current.main.dashboard', compact('tenant_count','tenants_latest'));
    }

}
