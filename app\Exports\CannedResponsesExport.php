<?php

namespace App\Exports;

use App\Models\Tenant\CannedResponse;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class CannedResponsesExport implements FromCollection, WithHeadings, WithMapping
{
    /**
     * @return \Illuminate\Support\Collection
     */

    private $incrementingId = 1;

    /**
     * Return the collection of responses.
     *
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return CannedResponse::all(); // Fetch all the responses
    }

    /**
     * Define the headings for the Excel sheet.
     *
     * @return array
     */
    public function headings(): array
    {
        return [
            'ID',          // Incrementing ID
            'Title',       // Processed HTML title
            'Content',     // Processed HTML content
            'Type',        // Type of response
            'Created At',  // Created at timestamp
        ];
    }

    /**
     * Map the data for each row.
     *
     * @param mixed $response
     * @return array
     */
    public function map($response): array
    {
        return [
            $this->incrementingId++,                    // Incrementing ID
            $this->previewHtml($response->title),       // Processed HTML title
            $this->previewHtml($response->content),     // Processed HTML content
            $response->type,                            // Response type
            $response->created_at->format('Y-m-d H:i'), // Created at timestamp formatted
        ];
    }

    /**
     * Function to preview HTML content.
     * - Strips out most HTML tags.
     * - Replaces image tags with their src attribute.
     * - Replaces anchor tags with the text (URL).
     */
    private function previewHtml($htmlContent)
    {
        // Handle tables - Convert <tr>, <td>, <th> to new lines and tabs for Excel
        $htmlContent = preg_replace('/<\/?(table|thead|tbody)[^>]*>/i', "\n", $htmlContent);
        $htmlContent = preg_replace('/<\/?(tr)[^>]*>/i', "\n", $htmlContent); // Rows separated by newline
        $htmlContent = preg_replace('/<\/?(td|th)[^>]*>/i', "\t", $htmlContent); // Cells separated by tab

        // Handle <div>, <p>, <br> as newlines for readability
        $htmlContent = preg_replace('/<\/?(div|p|br)[^>]*>/i', "\n", $htmlContent);

        // Handle links in the format: Text (URL)
        $htmlContent = preg_replace_callback('/<a[^>]*href=["\']?([^"\'>]+)["\']?[^>]*>([^<]+)<\/a>/i', function ($matches) {
            return $matches[2] . ' (' . $matches[1] . ')'; // Show link text (URL)
        }, $htmlContent);

        // Replace images with their source URL as [Image: URL]
        $htmlContent = preg_replace('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', '[Image: $1]', $htmlContent);

        // Strip remaining tags, leaving only the text
        $cleanedContent = strip_tags($htmlContent);

        // Decode HTML entities (like &nbsp; or &amp;)
        $cleanedContent = html_entity_decode($cleanedContent, ENT_QUOTES, 'UTF-8');

        // Replace multiple spaces and newlines with a single space where appropriate
        $cleanedContent = preg_replace('/\s+/', ' ', $cleanedContent);

        // Return the cleaned-up, trimmed content
        return trim($cleanedContent);
    }
}

