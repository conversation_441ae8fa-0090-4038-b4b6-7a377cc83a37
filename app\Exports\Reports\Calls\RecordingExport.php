<?php

namespace App\Exports\Reports\Calls;

use Illuminate\Support\Str;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class RecordingExport implements FromCollection, WithHeadings, WithMapping
{
    public $records;
    public $headings;

    public function __construct($records, $headings)
    {
        $this->records = $records;
        $this->headings = $headings;
    }

    public function collection()
    {
        return $this->records;
    }

    public function map($record): array
    {

        return [
            $record->user_id ?? 'N/A',
            $record->user ? (($record->user->first_name ?? '') . ' ' . ($record->user->last_name ?? '')) : 'N/A',
            $record->callID ?? 'N/A',
            ($record->voiceReport && $record->voiceReport->callerIDNumber) ? Str::title($record->voiceReport->callerIDNumber) : 'N/A',
            ($record->voiceReport && $record->voiceReport->callerIDName) ? Str::title($record->voiceReport->callerIDName) : 'N/A',
            ($record->voiceReport && $record->voiceReport->didCalled) ? Str::title($record->voiceReport->didCalled) : 'N/A',
            $record->created_at ? $record->created_at->toDateTimeString() : '-',
        ];
    }

    public function headings(): array
    {
        return $this->headings;
    }
} 