<?php

namespace App\Http\Controllers\Api\Supervisor\Statistics;

use App\Http\Controllers\Controller;
use App\Http\Resources\Supervisor\Search\ResourceResource;
use App\Http\Resources\Supervisor\Statistics\Dashboard\nonVoiceBehavior;
use App\Http\Resources\Supervisor\Statistics\Dashboard\ticketBehavior;
use App\Models\Tenant\AgentStatusAction;
use App\Models\Tenant\Resource;
use App\Models\Tenant\Ticket;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class DashboardController extends Controller
{
    public function nonVoiceBehavior(Request $request)
    {
        $channel = isset($request->channel) && $request->channel != '' ? $request->channel : null;
        $resources = Resource::with(['resourceInfo']);

        if ($channel != null) {
            $channel = Str::ucfirst(Str::lower($channel));
            $resources->whereHas('resourceInfo', function ($query) use ($channel) {
                $query->where('channel', 'like', '%' . $channel . '%');
            });
        }

        $today = Carbon::today();

        $new = clone $resources;
        $new = $new->whereDate('created_at', $today)->where('status', 'New')->count();

        $resolved = clone $resources;
        $resolved = $resolved->whereDate('created_at', $today)->where('status', 'Resolved')->count();

        $closed = clone $resources;
        $closed = $closed->whereDate('created_at', $today)->where('status', 'Closed')->count();

        $escalated = clone $resources;
        $escalated = $escalated->whereDate('created_at', $today)->where('status', 'New Ticket')->count();

        $unresolved = clone $resources;
        $unresolved = $unresolved->whereDate('created_at', $today)->where('status', '!=','Resolved')->count();

        $received = clone $resources;
        $received = $received->whereDate('created_at', $today)->count();

        $dueToday = clone $resources;
        $dueToday = $dueToday->whereDate('created_at', $today)->where('status', 'New')->count();

        $overDue = clone $resources;
        $overDue = $overDue->where('created_at', '!=', $today)->where('status', 'New')->count();


        $all = [
            'new' => ['total'=>$new ,'fontawesome'=>'fa'],
            'escalated' => ['total'=>$escalated ,'fontawesome'=>'fa'],
            'resolved' => ['total'=>$resolved ,'fontawesome'=>'fa'],
            'closed' => ['total'=>$closed ,'fontawesome'=>'fa'],
            'unresolved' => ['total'=>$unresolved ,'fontawesome'=>'fa'],
            'received' => ['total'=>$received ,'fontawesome'=>'fa'],
            'dueToday' => ['total'=>$dueToday ,'fontawesome'=>'fa'],
            'overDue' => ['total'=>$overDue ,'fontawesome'=>'fa'],
        ];

        return new nonVoiceBehavior($all);


    }

    public function nonVoiceTrends(Request $request)
    {
        // Get records for yesterday
        $yesterdayData = $this->getResourceByDate(Carbon::yesterday());

        // Get records for today
        $todayData = $this->getResourceByDate(Carbon::today());


        // Generate all hours of the day
        $allHours = array_map(function ($i) {
            return str_pad($i, 2, '0', STR_PAD_LEFT);
        }, range(0, 23));

        return response()->json(['data'=>[
            'yesterday' => $this->fillMissingHours($yesterdayData, $allHours),
            'today' => $this->fillMissingHours($todayData, $allHours),
        ]]);

    }

    public function nonVoiceResolution()
    {
        return response()->json(['data'=>[
            'ART' => 0,
            'RWS' => 0,
        ]]);
    }

    public function ticketBehavior()
    {
        $channel = isset($request->channel) && $request->channel != '' ? $request->channel : null;
        $resources = Ticket::with(['ticketInfo']);

        if ($channel != null) {
            $channel = Str::ucfirst(Str::lower($channel));
            $resources->whereHas('ticketInfo', function ($query) use ($channel) {
                $query->where('channel', 'like', '%' . $channel . '%');
            });
        }

        $today = Carbon::today();

        $new = clone $resources;
        $new = $new->whereDate('created_at', $today)->where('status', 'New')->count();

        $resolved = clone $resources;
        $resolved = $resolved->whereDate('created_at', $today)->where('status', 'Resolved')->count();

        $closed = clone $resources;
        $closed = $closed->whereDate('created_at', $today)->where('status', 'Closed')->count();

        $redirect = clone $resources;
        $redirect = $redirect->whereDate('created_at', $today)->where('status', 'Redirect')->count();

        $unresolved = clone $resources;
        $unresolved = $unresolved->whereDate('created_at', $today)->where('status', '!=','Resolved')->count();

        $received = clone $resources;
        $received = $received->whereDate('created_at', $today)->count();

        $dueToday = clone $resources;
        $dueToday = $dueToday->whereDate('created_at', $today)->where('status', 'New')->count();

        $overDue = clone $resources;
        $overDue = $overDue->where('created_at', '!=', $today)->where('status', 'New')->count();


        $all = [
            'new' => ['total'=>$new ,'fontawesome'=>'fa'],
            'redirect' => ['total'=>$redirect ,'fontawesome'=>'fa'],
            'resolved' => ['total'=>$resolved ,'fontawesome'=>'fa'],
            'closed' => ['total'=>$closed ,'fontawesome'=>'fa'],
            'unresolved' => ['total'=>$unresolved ,'fontawesome'=>'fa'],
            'received' => ['total'=>$received ,'fontawesome'=>'fa'],
            'dueToday' => ['total'=>$dueToday ,'fontawesome'=>'fa'],
            'overDue' => ['total'=>$overDue ,'fontawesome'=>'fa'],
        ];

        return new ticketBehavior($all);


    }

    public function ticketTrends()
    {
        // Get records for yesterday
        $yesterdayData = $this->getTicketByDate(Carbon::yesterday());

        // Get records for today
        $todayData = $this->getTicketByDate(Carbon::today());


        // Generate all hours of the day
        $allHours = array_map(function ($i) {
            return str_pad($i, 2, '0', STR_PAD_LEFT);
        }, range(0, 23));

        return response()->json(['data'=>[
            'yesterday' => $this->fillMissingHours($yesterdayData, $allHours),
            'today' => $this->fillMissingHours($todayData, $allHours),
        ]]);
    }

    public function ticketResolution()
    {
        return response()->json(['data'=>[
            'ART' => 0,
            'RWS' => 0,
        ]]);
    }

    public function voice()
    {
        return response()->json(['data'=>[]]);
    }

    public function agent()
    {

        $loggedin     = AgentStatusAction::where('agent_status_id','!=',2)->count();
        $available    = AgentStatusAction::where('agent_status_id',3)->count();
        $notAvailable = AgentStatusAction::where('agent_status_id',4)->count();
        $loggedout    = AgentStatusAction::where('agent_status_id',2)->count();


        return response()->json(['data'=>[
            'loggediIn'=>$loggedin,
            'available'=>$available,
            'notAvailable'=>$notAvailable,
            'loggedOut'=>$loggedout,
        ]]);

    }

    public function agentNonVoiceBehavior(Request $request)
    {
        $channel = isset($request->channel) && $request->channel != '' ? $request->channel : null;
        $resources = Resource::with(['resourceInfo'])->where('user_id',auth()->id());

        if ($channel != null) {
            $channel = Str::ucfirst(Str::lower($channel));
            $resources->whereHas('resourceInfo', function ($query) use ($channel) {
                $query->where('channel', 'like', '%' . $channel . '%');
            });
        }


        $today = Carbon::today();

        $new = clone $resources;
        $new = $new->whereDate('created_at', $today)->where('status', 'New')->count();

        $resolved = clone $resources;
        $resolved = $resolved->whereDate('created_at', $today)->where('status', 'Resolved')->count();

        $closed = clone $resources;
        $closed = $closed->whereDate('created_at', $today)->where('status', 'Closed')->count();

        $escalated = clone $resources;
        $escalated = $escalated->whereDate('created_at', $today)->where('status', 'New Ticket')->count();

        $unresolved = clone $resources;
        $unresolved = $unresolved->whereDate('created_at', $today)->where('status', '!=','Resolved')->count();

        $received = clone $resources;
        $received = $received->whereDate('created_at', $today)->count();

        $dueToday = clone $resources;
        $dueToday = $dueToday->whereDate('created_at', $today)->where('status', 'New')->count();

        $overDue = clone $resources;
        $overDue = $overDue->where('created_at', '!=', $today)->where('status', 'New')->count();


        $all = [
            'new' => ['total'=>$new ,'fontawesome'=>'fa'],
            'escalated' => ['total'=>$escalated ,'fontawesome'=>'fa'],
            'resolved' => ['total'=>$resolved ,'fontawesome'=>'fa'],
            'closed' => ['total'=>$closed ,'fontawesome'=>'fa'],
            'unresolved' => ['total'=>$unresolved ,'fontawesome'=>'fa'],
            'received' => ['total'=>$received ,'fontawesome'=>'fa'],
            'dueToday' => ['total'=>$dueToday ,'fontawesome'=>'fa'],
            'overDue' => ['total'=>$overDue ,'fontawesome'=>'fa'],
        ];

        return new nonVoiceBehavior($all);


    }

    public function agentNonVoiceTrends(Request $request)
    {
        // Get records for yesterday
        $yesterdayData = $this->getAgentResourceByDate(Carbon::yesterday());

        // Get records for today
        $todayData = $this->getAgentResourceByDate(Carbon::today());


        // Generate all hours of the day
        $allHours = array_map(function ($i) {
            return str_pad($i, 2, '0', STR_PAD_LEFT);
        }, range(0, 23));

        return response()->json(['data'=>[
            'yesterday' => $this->fillMissingHours($yesterdayData, $allHours),
            'today' => $this->fillMissingHours($todayData, $allHours),
        ]]);

    }

    public function agentNonVoiceResolution()
    {
        return response()->json(['data'=>[
            'ART' => 0,
            'RWS' => 0,
        ]]);
    }

    public function agentTicketBehavior()
    {
        $channel = isset($request->channel) && $request->channel != '' ? $request->channel : null;
        $resources = Ticket::with(['ticketInfo'])->where('user_id',auth()->id());

        if ($channel != null) {
            $channel = Str::ucfirst(Str::lower($channel));
            $resources->whereHas('ticketInfo', function ($query) use ($channel) {
                $query->where('channel', 'like', '%' . $channel . '%');
            });
        }

        $today = Carbon::today();

        $new = clone $resources;
        $new = $new->whereDate('created_at', $today)->where('status', 'New')->count();

        $resolved = clone $resources;
        $resolved = $resolved->whereDate('created_at', $today)->where('status', 'Resolved')->count();

        $closed = clone $resources;
        $closed = $closed->whereDate('created_at', $today)->where('status', 'Closed')->count();

        $redirect = clone $resources;
        $redirect = $redirect->whereDate('created_at', $today)->where('status', 'Redirect')->count();

        $unresolved = clone $resources;
        $unresolved = $unresolved->whereDate('created_at', $today)->where('status', '!=','Resolved')->count();

        $received = clone $resources;
        $received = $received->whereDate('created_at', $today)->count();

        $dueToday = clone $resources;
        $dueToday = $dueToday->whereDate('created_at', $today)->where('status', 'New')->count();

        $overDue = clone $resources;
        $overDue = $overDue->where('created_at', '!=', $today)->where('status', 'New')->count();


        $all = [
            'new' => ['total'=>$new ,'fontawesome'=>'fa'],
            'redirect' => ['total'=>$redirect ,'fontawesome'=>'fa'],
            'resolved' => ['total'=>$resolved ,'fontawesome'=>'fa'],
            'closed' => ['total'=>$closed ,'fontawesome'=>'fa'],
            'unresolved' => ['total'=>$unresolved ,'fontawesome'=>'fa'],
            'received' => ['total'=>$received ,'fontawesome'=>'fa'],
            'dueToday' => ['total'=>$dueToday ,'fontawesome'=>'fa'],
            'overDue' => ['total'=>$overDue ,'fontawesome'=>'fa'],
        ];

        return new ticketBehavior($all);


    }

    public function agentTicketTrends()
    {
        // Get records for yesterday
        $yesterdayData = $this->getAgentTicketByDate(Carbon::yesterday());

        // Get records for today
        $todayData = $this->getAgentTicketByDate(Carbon::today());


        // Generate all hours of the day
        $allHours = array_map(function ($i) {
            return str_pad($i, 2, '0', STR_PAD_LEFT);
        }, range(0, 23));

        return response()->json(['data'=>[
            'yesterday' => $this->fillMissingHours($yesterdayData, $allHours),
            'today' => $this->fillMissingHours($todayData, $allHours),
        ]]);
    }

    public function agentTicketResolution()
    {
        return response()->json(['data'=>[
            'ART' => 0,
            'RWS' => 0,
        ]]);
    }

    private function getResourceByDate($date)
    {
        $records = Resource::whereDate('created_at', $date)
            ->selectRaw('DATE_FORMAT(created_at, "%H") as hour, count(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->pluck('count', 'hour');

        return $records;
    }

    private function getTicketByDate($date)
    {
        $records = Ticket::whereDate('created_at', $date)
            ->selectRaw('DATE_FORMAT(created_at, "%H") as hour, count(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->pluck('count', 'hour');

        return $records;
    }

    private function getAgentResourceByDate($date)
    {
        $records = Resource::whereDate('created_at', $date)->where('user_id',auth()->id())
            ->selectRaw('DATE_FORMAT(created_at, "%H") as hour, count(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->pluck('count', 'hour');

        return $records;
    }

    private function getAgentTicketByDate($date)
    {
        $records = Ticket::whereDate('created_at', $date)->where('user_id',auth()->id())
            ->selectRaw('DATE_FORMAT(created_at, "%H") as hour, count(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->pluck('count', 'hour');

        return $records;
    }

    private function fillMissingHours($data, $allHours)
    {
        $result = [];

        foreach ($allHours as $hour) {
            $result[$hour] = $data[$hour] ?? 0;
        }

        return $result;
    }

}
