<?php

namespace App\Exports;

use App\Models\Tenant\Ticket;
use App\Models\Tenant\User;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use App\Models\Tenant\AgentStatusActionLog;
use App\Models\Tenant\ResourceInfo;
use App\Models\Tenant\AgentSession;
use App\Models\Tenant\AgentStatus;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;


class AgentsExport implements FromCollection, WithHeadings
{
    use Exportable;
    protected $data;


    public function __construct($data)
    {
        $this->data = $data;
    }


    public function collection()
    {
        //dd($this->data);

        if ($this->data['report_type'] == 'Agent Login/Logout') {

            $result = AgentSession::query()
            ->join('users', 'agent_sessions.user_id', '=', 'users.id')
            ->select(
                'agent_sessions.id',
                'users.first_name',
                'login_at',
                'logout_at'
            )
            ->get();


            return $result->map(function ($item) {
                return [
                    'ID' => $item->id,
                    'username' => $item->first_name,
                    'login_at' => $item->login_at,
                    'logout_at' => $item->logout_at,
                ];
            });
        }

        elseif ($this->data['report_type'] == 'Agent Status') {

            $diffWithNextRecord = AgentStatusActionLog::query()
                ->select([
                    AgentStatusActionLog::raw('DATE_FORMAT(s1.created_at, "%Y-%m-%d %H:00:00") AS hourly_date'),
                    's1.agent_status_id',
                    's1.user_id',
                    AgentStatusActionLog::raw('SEC_TO_TIME(SUM(TIMESTAMPDIFF(SECOND, s1.created_at, s2.created_at))) AS sum_diff_seconds'),
                    'agent_statuses.name AS status_name'
                ])
                ->from('agent_status_action_logs AS s1')
                ->join('agent_status_action_logs AS s2', function ($join) {
                    $join->on('s1.id', '=', AgentStatusActionLog::raw('(s2.id - 1)'))
                        ->where('s2.created_at', '>', 's1.created_at')
                        ->whereRaw('DATE_FORMAT(s1.created_at, "%Y-%m-%d %H:00:00") = DATE_FORMAT(s2.created_at, "%Y-%m-%d %H:00:00")');
                })
                ->join('agent_statuses', 's1.agent_status_id', '=', 'agent_statuses.id')
                ->groupBy('hourly_date', 's1.agent_status_id', 's1.user_id', 'status_name')
                ->whereDate('s1.created_at', '=', $this->data['from'])
                ->get();

            return  $groupedData = $diffWithNextRecord->groupBy(['user_id', 'hourly_date', 'status_name']);
        }


        elseif ($this->data['report_type'] == 'Agent Performance') {

            $diffWithNextRecord = AgentStatusActionLog::query()
                ->select([
                    AgentStatusActionLog::raw('DATE(s1.created_at) AS daily_date'),
                    's1.agent_status_id',
                    's1.user_id',
                    AgentStatusActionLog::raw('SEC_TO_TIME(SUM(TIMESTAMPDIFF(SECOND, s1.created_at, s2.created_at))) AS sum_diff_seconds'),
                    'agent_statuses.name AS status_name'
                ])
                ->from('agent_status_action_logs AS s1')
                ->join('agent_status_action_logs AS s2', function ($join) {
                    $join->on('s1.id', '=', AgentStatusActionLog::raw('(s2.id - 1)'))
                        ->where('s2.created_at', '>', 's1.created_at');
                })
                ->join('agent_statuses', 's1.agent_status_id', '=', 'agent_statuses.id')
                ->groupBy('daily_date', 's1.agent_status_id', 's1.user_id', 'status_name')
                ->get();
            $groupedData = $diffWithNextRecord->groupBy(['user_id', 'daily_date', 'status_name']);
            return $groupedData;
        }


        elseif ($this->data['report_type'] == 'Agent Daily Staffed') {

            $result = AgentSession::query()
            ->with('user')
            ->select(
                'agent_sessions.user_id',
                AgentSession::raw('DATE(agent_sessions.created_at) as day'),
                AgentSession::raw('SEC_TO_TIME(SUM(TIME_TO_SEC(TIMEDIFF(logout_at, login_at)))) as total_duration',
                'user.first_name')
            )
            ->whereBetween('agent_sessions.created_at', [$this->data['from'], $this->data['to']])
            ->groupBy('agent_sessions.user_id', 'day')
            ->get();

        return $result->map(function ($item) {
            return [
                'ID' => $item->user->id,
                'username' => $item->day,
                'login_at' => $item->first_name,
                'logout_at' => $item->total_duration,
            ];
        });
    }
    }
        public function headings(): array
    {
        if ($this->data['report_type'] == 'Agent Login/Logout') {
            return ['User ID', 'Name', 'Login At', 'Logout At'];
        }
        elseif ($this->data['report_type'] == 'Agent Status') {
            return ['Hourly Date', 'User ID', 'Status ID', 'Duration'];
        }
        elseif ($this->data['report_type'] == 'Agent Performance') {
            return ['Daily Date', 'User ID', 'Status ID', 'Duration'];
        }
        elseif ($this->data['report_type'] == 'Agent Daily Staffed') {
            return ['User ID', 'Day','User name', 'Total Duration'];
        }

      //  return [];
    }

}
