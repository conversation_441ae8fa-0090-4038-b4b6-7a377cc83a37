<?php

namespace App\Http\Controllers\Tenant\CSAT;

use App\Http\Controllers\Controller;
use App\Models\Tenant\CSAT;
use App\Models\Tenant\CSATQuestion;
use App\Models\Tenant\Resource;
use Illuminate\Http\Request;

class CsatController extends Controller
{
    public function NewSurvey($id)
    {
//        $resource = Resource::where('id',$id)->first();
//        $csat = CSAT::first();
//        return view('tenant.c-sat.index-2',compact('csat' ,'resource'));

//        dd(encrypt(1783, config('app.key')));
        try {
            $value = decrypt($id, config('app.key'));
//            dd($value);
        }catch (\Exception $e)
        {
            return abort(404);
        }



        $resource = Resource::where('id',$value)->first();

        if ($resource){
            if ($resource->satisfaction == null){
                $csat = CSAT::first();
                $csatQ = CSATQuestion::where('status',1)->get();
                if ($csat){
                    if ($csat->status){
                        return view('tenant.c-sat.index',compact('csat' ,'resource','csatQ'));
                    }else{
                        return abort(404);
                    }
                }else{
                    return abort(404);
                }
            }else{
                $csat = CSAT::first();
                return view('tenant.c-sat.index-2',compact('csat' ,'resource'));
//                return abort(404);
            }
        }else{
            return abort(404);
        }

    }
}
