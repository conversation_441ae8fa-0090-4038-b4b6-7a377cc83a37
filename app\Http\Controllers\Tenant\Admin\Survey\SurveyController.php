<?php

namespace App\Http\Controllers\Tenant\Admin\Survey;

use App\Http\Controllers\Controller;
use App\Models\Tenant\CSAT;
use Illuminate\Http\Request;

class SurveyController extends Controller
{
    public function index()
    {
        return view('tenant.admin.survey.index');
    }



    public function Region()
    {
        return view('tenant.admin.survey.region.index');
    }
    public function Survey()
    {
        return view('tenant.admin.survey.survey.index');
    }
    public function SurveyQuestion()
    {
        return view('tenant.admin.survey.survey-question.index');
    }
    public function SurveyAnswer()
    {
        return view('tenant.admin.survey.survey-answer.index');
    }
    public function SurveySubmit($survey_id)
    {

        return view('tenant.survey-public', compact('survey_id'));
    }
}
