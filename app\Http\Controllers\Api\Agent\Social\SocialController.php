<?php

namespace App\Http\Controllers\Api\Agent\Social;

use App\Http\Controllers\Controller;
use App\Http\Resources\Agent\NoneVoice\NonVoiceDetailsResource;
use App\Http\Resources\Agent\NoneVoice\NonVoiceListResource;
use App\Http\Resources\Supervisor\Tickets\TicketListResource;
use App\Models\Tenant\Resource;
use Illuminate\Http\Request;

class SocialController extends Controller
{

    public function socialList(Request $request)
    {

        $channel = isset($request->channel) && $request->channel != '' ? $request->channel : null;
        $status = isset($request->status) && $request->status != '' ? $request->status : null;
        $fromDate = isset($request->fromDate) && $request->fromDate != '' ? $request->fromDate : null;
        $toDate = isset($request->toDate) && $request->toDate != '' ? $request->toDate : null;

        $limit = isset($request->limit) && $request->limit != '' ? $request->limit : 100;

        $tickets = Resource::with(['resourceInfo'])->where('user_id',auth()->id());


        if ($channel != null) {
            $tickets->whereHas('resourceInfo', function ($query) use ($channel) {
                $query->where('channel',$channel )->where('channel','!=','Email' )->where('channel','!=','Livechat')->where('channel','!=','Whatsapp' );
            });
        }else{
            $tickets->whereHas('resourceInfo', function ($query)  {
                $query->where('channel','!=','Email' )->where('channel','!=','Livechat')->where('channel','!=','Whatsapp' );
            });
        }

        if ($fromDate != null) {
            $fromDate = date($fromDate . ' 00:00:00');
            $tickets->where('created_at', '>=', $fromDate);
        }

        if ($status != null) {
            $tickets->where('status', $status);
        }

        if ($toDate != null) {
            $toDate = date($toDate . ' 23:59:59');
            $tickets->where('created_at', '<=', $toDate);
        }

        $tickets = $tickets->orderBy('id', 'desc')->limit($limit)->get();


        if ($tickets->count() > 0) {
            return NonVoiceListResource::collection($tickets);
        } else {
            return response()->json(['error' => true, 'message' => 'No Social found'], 200);
        }

    }
    public function socialDetails(Request $request)
    {

        $tickets = Resource::where('id',$request->id)->with(['resourceInfo','resourceActions','resourceCategories', 'resourceFields']);

        $tickets->whereHas('resourceInfo', function ($query)  {
            $query->where('channel','!=','Email' )->where('channel','!=','Livechat')->where('channel','!=','Whatsapp' );
        });

        $tickets = $tickets->orderBy('id', 'desc')->where('user_id',auth()->id())->first();

        if ($tickets) {
            return new NonVoiceDetailsResource($tickets);
        } else {
            return response()->json(['error' => true, 'message' => 'No Email found'], 200);
        }

    }

    public function chatList(Request $request)
    {


        $channel = isset($request->channel) && $request->channel != '' ? $request->channel : null;
        $status = isset($request->status) && $request->status != '' ? $request->status : null;
        $fromDate = isset($request->fromDate) && $request->fromDate != '' ? $request->fromDate : null;
        $toDate = isset($request->toDate) && $request->toDate != '' ? $request->toDate : null;

        $limit = isset($request->limit) && $request->limit != '' ? $request->limit : 100;

        $tickets = Resource::with(['resourceInfo'])->where('user_id',auth()->id());


        if ($channel != null) {
            $tickets->whereHas('resourceInfo', function ($query) use ($channel) {
                $query->where('channel',$channel )->where('channel','!=','Email' )->where('channel','!=','Facebook')->where('channel','!=','Instagram' )->where('channel','!=','Google' );
            });
        }else{
            $tickets->whereHas('resourceInfo', function ($query)  {
                $query->where('channel','!=','Email' )->where('channel','!=','Facebook')->where('channel','!=','Instagram' )->where('channel','!=','Google' );
            });
        }

        if ($fromDate != null) {
            $fromDate = date($fromDate . ' 00:00:00');
            $tickets->where('created_at', '>=', $fromDate);
        }

        if ($status != null) {
            $tickets->where('status', $status);
        }

        if ($toDate != null) {
            $toDate = date($toDate . ' 23:59:59');
            $tickets->where('created_at', '<=', $toDate);
        }

        $tickets = $tickets->orderBy('id', 'desc')->limit($limit)->get();


        if ($tickets->count() > 0) {
            return NonVoiceListResource::collection($tickets);
        } else {
            return response()->json(['error' => true, 'message' => 'No Chat found'], 200);
        }

    }
    public function chatDetails(Request $request)
    {


        $tickets = Resource::where('id',$request->id)->with(['resourceInfo','resourceActions','resourceCategories', 'resourceFields']);

        $tickets->whereHas('resourceInfo', function ($query)  {
            $query->where('channel','!=','Email' )->where('channel','!=','Facebook')->where('channel','!=','Instagram' )->where('channel','!=','Google' );
        });

        $tickets = $tickets->orderBy('id', 'desc')->where('user_id',auth()->id())->first();


        if ($tickets) {
            return new NonVoiceDetailsResource($tickets);
        } else {
            return response()->json(['error' => true, 'message' => 'No Chat found'], 200);
        }


    }

    public function emailList(Request $request)
    {


        $channel = isset($request->channel) && $request->channel != '' ? $request->channel : null;
        $status = isset($request->status) && $request->status != '' ? $request->status : null;
        $fromDate = isset($request->fromDate) && $request->fromDate != '' ? $request->fromDate : null;
        $toDate = isset($request->toDate) && $request->toDate != '' ? $request->toDate : null;

        $limit = isset($request->limit) && $request->limit != '' ? $request->limit : 100;

        $tickets = Resource::with(['resourceInfo'])->where('user_id',auth()->id());


        if ($channel != null) {
            $tickets->whereHas('resourceInfo', function ($query) use ($channel) {
                $query->where('channel',$channel )->where('channel','!=','Livechat')->where('channel','!=','Whatsapp' )->where('channel','!=','Facebook')->where('channel','!=','Instagram' )->where('channel','!=','Google' );
            });
        }else{
            $tickets->whereHas('resourceInfo', function ($query)  {
                $query->where('channel','!=','Facebook')->where('channel','!=','Instagram' )->where('channel','!=','Google' )->where('channel','!=','Livechat')->where('channel','!=','Whatsapp' );
            });
        }

        if ($fromDate != null) {
            $fromDate = date($fromDate . ' 00:00:00');
            $tickets->where('created_at', '>=', $fromDate);
        }

        if ($status != null) {
            $tickets->where('status', $status);
        }

        if ($toDate != null) {
            $toDate = date($toDate . ' 23:59:59');
            $tickets->where('created_at', '<=', $toDate);
        }

        $tickets = $tickets->orderBy('id', 'desc')->limit($limit)->get();


        if ($tickets->count() > 0) {
            return NonVoiceListResource::collection($tickets);
        } else {
            return response()->json(['error' => true, 'message' => 'No Email found'], 200);
        }

    }
    public function emailDetails(Request $request)
    {


        $tickets = Resource::where('id',$request->id)->with(['resourceInfo','resourceActions','resourceCategories', 'resourceFields']);

        $tickets->whereHas('resourceInfo', function ($query)  {
            $query->where('channel','!=','Facebook')->where('channel','!=','Instagram' )->where('channel','!=','Google' )->where('channel','!=','Livechat')->where('channel','!=','Whatsapp' );
        });

        $tickets = $tickets->orderBy('id', 'desc')->where('user_id',auth()->id())->first();
//        dd($tickets);

        if ($tickets) {
            return new NonVoiceDetailsResource($tickets);
        } else {
            return response()->json(['error' => true, 'message' => 'No Email found'], 200);
        }

    }


    public function socialListS(Request $request)
    {

        $channel = isset($request->channel) && $request->channel != '' ? $request->channel : null;
        $status = isset($request->status) && $request->status != '' ? $request->status : null;
        $fromDate = isset($request->fromDate) && $request->fromDate != '' ? $request->fromDate : null;
        $toDate = isset($request->toDate) && $request->toDate != '' ? $request->toDate : null;

        $limit = isset($request->limit) && $request->limit != '' ? $request->limit : 100;

        $tickets = Resource::with(['resourceInfo']);


        if ($channel != null) {
            $tickets->whereHas('resourceInfo', function ($query) use ($channel) {
                $query->where('channel',$channel )->where('channel','!=','Email' )->where('channel','!=','Livechat')->where('channel','!=','Whatsapp' );
            });
        }else{
            $tickets->whereHas('resourceInfo', function ($query)  {
                $query->where('channel','!=','Email' )->where('channel','!=','Livechat')->where('channel','!=','Whatsapp' );
            });
        }

        if ($fromDate != null) {
            $fromDate = date($fromDate . ' 00:00:00');
            $tickets->where('created_at', '>=', $fromDate);
        }

        if ($status != null) {
            $tickets->where('status', $status);
        }

        if ($toDate != null) {
            $toDate = date($toDate . ' 23:59:59');
            $tickets->where('created_at', '<=', $toDate);
        }

        // $tickets = $tickets->orderBy('id', 'desc')->limit($limit)->get();
        $tickets = $tickets->orderBy('id', 'desc')->paginate($limit);


        if ($tickets->count() > 0) {
            return NonVoiceListResource::collection($tickets);
        } else {
            return response()->json(['error' => true, 'message' => 'No Social found'], 200);
        }

    }
    public function socialDetailsS(Request $request)
    {

        $tickets = Resource::where('id',$request->id)->with(['resourceInfo','resourceActions','resourceCategories', 'resourceFields']);

        $tickets = $tickets->orderBy('id', 'desc')->first();

        if ($tickets) {
            return new NonVoiceDetailsResource($tickets);
        } else {
            return response()->json(['error' => true, 'message' => 'No Email found'], 200);
        }

    }

    public function chatListS(Request $request)
    {


        $channel = isset($request->channel) && $request->channel != '' ? $request->channel : null;
        $status = isset($request->status) && $request->status != '' ? $request->status : null;
        $fromDate = isset($request->fromDate) && $request->fromDate != '' ? $request->fromDate : null;
        $toDate = isset($request->toDate) && $request->toDate != '' ? $request->toDate : null;

        $limit = isset($request->limit) && $request->limit != '' ? $request->limit : 100;

        $tickets = Resource::with(['resourceInfo']);


        if ($channel != null) {
            $tickets->whereHas('resourceInfo', function ($query) use ($channel) {
                $query->where('channel',$channel )->where('channel','!=','Email' )->where('channel','!=','Facebook')->where('channel','!=','Instagram' )->where('channel','!=','Google' );
            });
        }else{
            $tickets->whereHas('resourceInfo', function ($query)  {
                $query->where('channel','!=','Email' )->where('channel','!=','Facebook')->where('channel','!=','Instagram' )->where('channel','!=','Google' );
            });
        }

        if ($fromDate != null) {
            $fromDate = date($fromDate . ' 00:00:00');
            $tickets->where('created_at', '>=', $fromDate);
        }

        if ($status != null) {
            $tickets->where('status', $status);
        }

        if ($toDate != null) {
            $toDate = date($toDate . ' 23:59:59');
            $tickets->where('created_at', '<=', $toDate);
        }

        // $tickets = $tickets->orderBy('id', 'desc')->limit($limit)->get();
        $tickets = $tickets->orderBy('id', 'desc')->paginate($limit);

        if ($tickets->count() > 0) {
            return NonVoiceListResource::collection($tickets);
        } else {
            return response()->json(['error' => true, 'message' => 'No Chat found'], 200);
        }

    }
    public function chatDetailsS(Request $request)
    {


        $tickets = Resource::where('id',$request->id)->with(['resourceInfo','resourceActions','resourceCategories', 'resourceFields']);

        $tickets = $tickets->orderBy('id', 'desc')->first();
//        dd($tickets);

        if ($tickets) {
            return new NonVoiceDetailsResource($tickets);
        } else {
            return response()->json(['error' => true, 'message' => 'No Chat found'], 200);
        }


    }

    public function emailListS(Request $request)
    {


        $channel = isset($request->channel) && $request->channel != '' ? $request->channel : null;
        $status = isset($request->status) && $request->status != '' ? $request->status : null;
        $fromDate = isset($request->fromDate) && $request->fromDate != '' ? $request->fromDate : null;
        $toDate = isset($request->toDate) && $request->toDate != '' ? $request->toDate : null;

        $limit = isset($request->limit) && $request->limit != '' ? $request->limit : 100;

        $tickets = Resource::with(['resourceInfo']);


        if ($channel != null) {
            $tickets->whereHas('resourceInfo', function ($query) use ($channel) {
                $query->where('channel',$channel )->where('channel','!=','Livechat')->where('channel','!=','Whatsapp' )->where('channel','!=','Facebook')->where('channel','!=','Instagram' )->where('channel','!=','Google' );
            });
        }else{
            $tickets->whereHas('resourceInfo', function ($query)  {
                $query->where('channel','!=','Facebook')->where('channel','!=','Instagram' )->where('channel','!=','Google' )->where('channel','!=','Livechat')->where('channel','!=','Whatsapp' );
            });
        }

        if ($fromDate != null) {
            $fromDate = date($fromDate . ' 00:00:00');
            $tickets->where('created_at', '>=', $fromDate);
        }

        if ($status != null) {
            $tickets->where('status', $status);
        }

        if ($toDate != null) {
            $toDate = date($toDate . ' 23:59:59');
            $tickets->where('created_at', '<=', $toDate);
        }

        // $tickets = $tickets->orderBy('id', 'desc')->limit($limit)->get();
        $tickets = $tickets->orderBy('id', 'desc')->paginate($limit);

        if ($tickets->count() > 0) {
            return NonVoiceListResource::collection($tickets);
        } else {
            return response()->json(['error' => true, 'message' => 'No Email found'], 200);
        }

    }
    public function emailDetailsS(Request $request)
    {


        $tickets = Resource::where('id',$request->id)->with(['resourceInfo','resourceActions','resourceCategories', 'resourceFields']);

        $tickets = $tickets->orderBy('id', 'desc')->first();
//        dd($tickets);

        if ($tickets) {
            return new NonVoiceDetailsResource($tickets);
        } else {
            return response()->json(['error' => true, 'message' => 'No Email found'], 200);
        }

    }
}
