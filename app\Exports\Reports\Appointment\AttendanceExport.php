<?php

namespace App\Exports\Reports\Appointment;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Illuminate\Support\Collection;

class AttendanceExport implements FromCollection, WithMapping, WithHeadings
{
    public $appointments;

    public function __construct($appointments)
    {
        $this->appointments = $appointments;
    }

    public function collection()
    {
        // Convert the appointments array into a collection for Excel export
        return collect([$this->appointments]);
    }

    public function map($row): array
    {
        // Ensure that the values are correctly returned as an array
        return [
            $row['total'],
            $row['canceled'],
            $row['booked'],
            $row['confirmed'],
        ];
    }

    public function headings(): array
    {
        return [
            'Total Appointment',
            'Canceled %',
            'Booked %',
            'Confirmed %',
        ];
    }
}
