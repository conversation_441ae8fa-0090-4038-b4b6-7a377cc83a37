<?php

namespace App\Exports;

use App\Models\Tenant\User;
use App\Models\Tenant\WfmBreak;
use App\Models\Tenant\WfmSwap;
use Maatwebsite\Excel\Concerns\FromCollection;
use Illuminate\Support\Facades\Request;
use Maatwebsite\Excel\Concerns\WithHeadings;

class SwapSummaryExport implements FromCollection, WithHeadings
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public $defaultAccount;

    public function collection()
    {
        $result = User ::with('swaps')->whereHas('roles',function ($q){$q->where('allowed_route','agent');})->get();
        $url = Request::url();
        $this->defaultAccount = explode('.', parse_url($url, PHP_URL_HOST))[0];


        return $result->map(function ($item) {
      
            return [
                'Agent Name' =>''.$item->first_name.' '.$item->last_name.'',
                'User Id' => $item->username,
                'Swap Reques' =>$item->swaps->count(),
                '# Approved' =>$item->swaps->where('status', 'approved')?   $item->swaps->where('status', 'approved')->count():0,
                '# Rejecte' =>$item->swaps->where('status', 'rejected') ? $item->swaps->where('status', 'rejected')->count():0,
                '# Pinding' =>$item->swaps->where('status', 'rejected') ? $item->swaps->where('status', 'pending')->count() :0,

     
         
            ];
       
        });
        
    }

    public function headings(): array
    {
            return [
                'Agent Name',
                'User Id' ,
                'Swap Reques' ,
                '# Approved',
                '# Rejecte' ,
                '# Pinding',
        ];
    }
}
