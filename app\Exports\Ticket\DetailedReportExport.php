<?php

namespace App\Exports\Ticket;

use App\Models\Tenant\Ticket;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class DetailedReportExport implements FromCollection, WithHeadings, WithMapping
{
    protected $levels;
    protected $from;
    protected $to;
    protected $lastKey;
    protected $maxIds = [];
    protected $category_array = [];

    public function __construct($levels, $from, $to,$lastKey,$maxIds,$category_array)
    {
        $this->levels = $levels;
        $this->from = $from;
        $this->to = $to;
        $this->lastKey = $lastKey;
        $this->maxIds = $maxIds;
        $this->category_array = $category_array;
    }
    public function collection()
    {
        $lastKey = $this->lastKey;
        $maxIds = $this->maxIds;
        return Ticket::with(['ticketCustomer', 'ticketInfo', 'ticketActions'])
            ->whereHas('ticketInfo', function ($q) {
                $q->where('created_at', '>=', $this->from." 00:00:00")
                  ->where('created_at', '<=', $this->to." 23:59:59");
            })
            ->when($lastKey !== null, function ($query) use ($lastKey, $maxIds) {
                $query->whereHas('ticketCategories', function ($q) use ($lastKey, $maxIds) {
                    $q->where('category_id', $this->category_array[$lastKey])
                    ->whereIn('id', $maxIds);
                });
            })
            ->get();
    }

    public function headings(): array
    {
        $headings = [
            'Ticket ID',
            'Channel',
            'Type',
            'Customer Name',
            'Status',
            'Department',
            'Priority',
            'Country',
            'Language',
        ];

        foreach ($this->levels as $level) {
            $headings[] = $level->label; // Dynamic headings for levels
        }

        $headings[] = 'User';
        $headings[] = 'Date';

        return $headings;
    }

    public function map($ticket): array
    {
        $mapped = [
            $ticket->id,
            $ticket->ticketInfo->channel,
            $ticket->ticketInfo->type,
            isset($ticket->ticketCustomer) ? $ticket->ticketCustomer->display_name : '-',
            $ticket->status,
            $ticket->ticketActions()->where('department_id', '!=', 0)->latest()->first()->department->name ?? '',
            $ticket->ticketInfo->priority,
            $ticket->ticketInfo->country,
            $ticket->ticketInfo->language,
        ];

        foreach ($this->levels as $level) {
            $category = $ticket->ticketCategories()->where('category_level_id', $level->id)->first();
            $mapped[] = $category ? $category->category->name : '-';
        }

        $lastAction = $ticket->ticketActions()->latest()->first();
        $mapped[] = $lastAction ? $lastAction->user->first_name . ' ' . $lastAction->user->last_name : '-';
        $mapped[] = $lastAction ? $lastAction->updated_at->format('Y-m-d H:i:s') : '-';

        return $mapped;
    }
}
