<?php

namespace App\Exports\Users\Supervisor;

use App\Models\Tenant\Department;
use App\Models\Tenant\Role;
use App\Models\User;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ImportTemplateExport implements FromCollection, WithHeadings, WithMapping
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        // You can return an empty collection or just create a dummy row for the template
        return collect([new \stdClass()]);
    }

    /**
     * Map the data to include roles and departments
     *
     * @param  mixed  $user
     * @return array
     */
    public function map($user): array
    {
        // Fetch roles and departments outside of the map method to avoid redundant queries
        $roles = Role::query()
            ->where('name', '!=', 'admin')
            ->where('name', '!=', 'agent')
            ->where('name', '!=', 'client')
            ->pluck('display_name')
            ->implode(',');

        $departments = Department::query()
            ->whereStatus(1)
            ->pluck('name')
            ->implode(',');

        return [
            '',
            '',
            '',
            '',
            '',
            'Aa@123456789',
            'Active,Inactive',
            $roles,
            $departments,
        ];
    }

    /**
     * Define the headers for the export
     *
     * @return array
     */
    public function headings(): array
    {
        return [
            'First Name',
            'Last Name',
            'Username',
            'Email',
            'Mobile',
            'Password',
            'Status',
            'Role',
            'Departments',
        ];
    }
}
