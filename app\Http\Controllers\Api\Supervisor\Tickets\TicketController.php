<?php

namespace App\Http\Controllers\Api\Supervisor\Tickets;

use App\Http\Controllers\Controller;
use App\Http\Resources\Supervisor\Tickets\TicketDetailsResource;
use App\Http\Resources\Supervisor\Tickets\TicketList;
use App\Http\Resources\Supervisor\Tickets\TicketListResource;
use App\Http\Resources\Supervisor\Tickets\TicketListResourceDevelopment;
use App\Models\Tenant\Ticket;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class TicketController extends Controller
{

    public function list(Request $request)
    {


        $channel = isset($request->channel) && $request->channel != '' ? $request->channel : null;

        $queryFilter = isset($request->queryFilter) && $request->queryFilter != '' ? $request->queryFilter : null;

        $status = isset($request->status) && $request->status != '' ? $request->status : null;

        $departmentId = isset($request->departmentId) && $request->departmentId != '' ? $request->departmentId : null;
        $fromDate = isset($request->fromDate) && $request->fromDate != '' ? $request->fromDate : null;
        $toDate = isset($request->toDate) && $request->toDate != '' ? $request->toDate : null;

        $limit = isset($request->limit) && $request->limit != '' ? $request->limit : 100;

        $ticket_id = isset($request->ticket_id) && $request->ticket_id !='' ? $request->ticket_id : null;

        $tickets = Ticket::whereHas('ticketInfo')->with(['ticketInfo']);


        if ($departmentId != null) {
            $tickets->where('department_id', $departmentId);
        }

        if ($ticket_id != null) {
            $tickets->where('id', $ticket_id);
        }
        if ($channel != null) {
            $tickets->whereHas('ticketInfo', function ($query) use ($channel) {
                $query->where('channel',$channel );
            });
        }

        if ($status != null) {
            $tickets->where('status', $status);
        }


        if ($queryFilter != null) {
            $string = explode(',', $queryFilter);
            $st = [];

            foreach ($string as $str)
            {
                $ega = explode('equal', $str);

                if (isset($ega[0]) &&isset($ega[1]))
                    $st[trim(Str::slug($ega[0]))] = trim($ega[1]);
            }


            foreach ($st as $key => $value)
            {
                $fullKy = "\"" . $key ."\": \"".$value ;
                // $fullKy2 = "\"" . $key ."\":\"".$value ;

                // dd($fullKy);

                $tickets->whereHas('ticketFiled', function ($query) use ($fullKy) {
                    $query->where('fields',"like", "%".$fullKy."%" );
                });


                // $tickets->whereHas('ticketFiled', function ($query) use ($fullKy2) {
                //     $query->where('fields',"like", "%".$fullKy2."%" );
                // });

            }

        }

        if ($fromDate != null) {
            $fromDate = date($fromDate . ' 00:00:00');
            $tickets->where('created_at', '>=', $fromDate);
        }


        if ($toDate != null) {
            $toDate = date($toDate . ' 23:59:59');
            $tickets->where('created_at', '<=', $toDate);
        }

        $tickets = $tickets->orderBy('id', 'desc')->paginate($limit);


        if ($tickets->count() > 0) {
            return TicketListResource::collection($tickets);
        } else {
            return response()->json(['error' => true, 'message' => 'No tickets found'], 200);
        }

    }

    public function developmentList(Request $request)
    {


        $channel = isset($request->channel) && $request->channel != '' ? $request->channel : null;

        $queryFilter = isset($request->queryFilter) && $request->queryFilter != '' ? $request->queryFilter : null;

        $status = isset($request->status) && $request->status != '' ? $request->status : null;

        $departmentId = isset($request->departmentId) && $request->departmentId != '' ? $request->departmentId : null;
        $fromDate = isset($request->fromDate) && $request->fromDate != '' ? $request->fromDate : null;
        $toDate = isset($request->toDate) && $request->toDate != '' ? $request->toDate : null;

        $limit = isset($request->limit) && $request->limit != '' ? $request->limit : 100;
        $ticket_id = isset($request->ticket_id) && $request->ticket_id !='' ? $request->ticket_id : null;

        $tickets = Ticket::whereHas('ticketInfo')->with(['ticketInfo']);

        if ($ticket_id != null) {
            $tickets->where('id', $ticket_id);
        }


        if ($departmentId != null) {
            $tickets->where('department_id', $departmentId);
        }

        if ($channel != null) {
            $tickets->whereHas('ticketInfo', function ($query) use ($channel) {
                $query->where('channel',$channel );
            });
        }

        if ($status != null) {
            $tickets->where('status', $status);
        }


        if ($queryFilter != null) {
            $string = explode(',', $queryFilter);
            $st = [];

            foreach ($string as $str)
            {
                $ega = explode('equal', $str);

                if (isset($ega[0]) &&isset($ega[1]))
                    $st[trim(Str::slug($ega[0]))] = trim($ega[1]);
            }


            foreach ($st as $key => $value)
            {
                $fullKy = "\"" . $key ."\": \"".$value ;
                // $fullKy2 = "\"" . $key ."\":\"".$value ;

                // dd($fullKy);

                $tickets->whereHas('ticketFiled', function ($query) use ($fullKy) {
                    $query->where('fields',"like", "%".$fullKy."%" );
                });


                // $tickets->whereHas('ticketFiled', function ($query) use ($fullKy2) {
                //     $query->where('fields',"like", "%".$fullKy2."%" );
                // });

            }

        }

        if ($fromDate != null) {
            $fromDate = date($fromDate . ' 00:00:00');
            $tickets->where('created_at', '>=', $fromDate);
        }


        if ($toDate != null) {
            $toDate = date($toDate . ' 23:59:59');
            $tickets->where('created_at', '<=', $toDate);
        }

        $tickets = $tickets->orderBy('id', 'desc')->paginate($limit);


        if ($tickets->count() > 0) {
            return TicketListResourceDevelopment::collection($tickets);
        } else {
            return response()->json(['error' => true, 'message' => 'No tickets found'], 200);
        }

    }
    public function agentList(Request $request)
    {

        $channel = isset($request->channel) && $request->channel != '' ? $request->channel : null;
        $status = isset($request->status) && $request->status != '' ? $request->status : null;
        $departmentId = isset($request->departmentId) && $request->departmentId != '' ? $request->departmentId : null;
        $fromDate = isset($request->fromDate) && $request->fromDate != '' ? $request->fromDate : null;
        $toDate = isset($request->toDate) && $request->toDate != '' ? $request->toDate : null;

        $limit = isset($request->limit) && $request->limit != '' ? $request->limit : 100;

        $tickets = Ticket::whereHas('ticketInfo')->with(['ticketInfo'])->where('user_id',auth()->id());


        if ($departmentId != null) {
            $tickets->where('department_id', $departmentId);
        }

        if ($channel != null) {
            $tickets->whereHas('ticketInfo', function ($query) use ($channel) {
                $query->where('channel',$channel );
            });
        }

        if ($fromDate != null) {
            $fromDate = date($fromDate . ' 00:00:00');
            $tickets->where('created_at', '>=', $fromDate);
        }

        if ($status != null) {
            $tickets->where('status', $status);
        }

        if ($toDate != null) {
            $toDate = date($toDate . ' 23:59:59');
            $tickets->where('created_at', '<=', $toDate);
        }

        // $tickets = $tickets->orderBy('id', 'desc')->limit($limit)->get();
        $tickets = $tickets->orderBy('id', 'desc')->paginate($limit);

        if ($tickets->count() > 0) {
            return TicketListResource::collection($tickets);
        } else {
            return response()->json(['error' => true, 'message' => 'No tickets found'], 200);
        }

    }

    public function details(Request $request)
    {


        $tickets = Ticket::whereHas('ticketInfo')->where('id',$request->id)->with(['ticketInfo','ticketCategories','ticketFiled', 'ticketActions' => function ($q) {
            $q->with(['fields', 'categories']);
        }]);



        $tickets = $tickets->orderBy('id', 'desc')->first();


        if ($tickets) {
            return new TicketDetailsResource($tickets);
        } else {
            return response()->json(['error' => true, 'message' => 'No tickets found'], 200);
        }

    }
    public function agentDetails(Request $request)
    {

        $tickets = Ticket::whereHas('ticketInfo')->where('id',$request->id)->with(['ticketInfo','ticketCategories','ticketFiled', 'ticketActions' => function ($q) {
            $q->with(['fields', 'categories']);
        }]);

        $tickets = $tickets->orderBy('id', 'desc')->where('user_id',auth()->id())->first();


        if ($tickets) {
            return new TicketDetailsResource($tickets);
        } else {
            return response()->json(['error' => true, 'message' => 'No tickets found'], 200);
        }

    }

    public function listClosedTicket(Request $request)
    {

        $category_id = $request->category_id ?? null;
        $fromDate = $request->fromDate ?? null;
        $toDate = $request->toDate ?? null;

        $tickets = Ticket::whereHas('ticketInfo')->with(['ticketInfo', 'ticketCategories', 'ticketActions']);

        if ($fromDate) {
            $fromDate = date($fromDate . ' 00:00:00');
            $tickets->where('created_at', '>=', $fromDate);
        }
        if ($toDate) {
            $toDate = date($toDate . ' 23:59:59');
            $tickets->where('created_at', '<=', $toDate);
        }
        if ($category_id) {
            $tickets->whereHas('ticketCategories', function ($query) use ($category_id) {
                $query->where('category_id', $category_id);
            });
        }

        $tickets = $tickets->orderBy('id', 'desc')->where('status','Closed')->get();

        if ($tickets->count() > 0) {
            return TicketList::collection($tickets);
        } else {
            return response()->json(['error' => true, 'message' => 'No tickets found'], 200);
        }


    }
}
