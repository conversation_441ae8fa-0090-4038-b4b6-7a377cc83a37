<?php

namespace App\Http\Controllers\Api\Supervisor\Filters;

use App\Http\Controllers\Controller;
use App\Http\Resources\Agent\Filters\ChatSource;
use App\Http\Resources\Agent\Filters\SocailSource;
use App\Http\Resources\Supervisor\Filters\NonVoiceSource;
use App\Http\Resources\Supervisor\Filters\TicketDepartment;
use App\Http\Resources\Supervisor\Filters\TicketSource;
use App\Http\Resources\Supervisor\Filters\voiceSource;
use App\Http\Resources\Supervisor\Search\TicketResource;
use App\Http\Resources\UserResource;
use App\Models\Tenant\Department;
use App\Models\Tenant\User;
use Illuminate\Http\Request;

class FilterController extends Controller
{
    public function ticketSource()
    {
        return new TicketSource([]);
    }

    public function socialSource()
    {
        return new SocailSource([]);
    }

    public function chatSource()
    {
        return new ChatSource([]);
    }

    public function nonVoiceStatistics()
    {
        return new NonVoiceSource([]);
    }

    public function ticketStatistics()
    {
        return new TicketSource([]);
    }

    public function voiceStatistics()
    {
        return new voiceSource([]);
    }


    public function ticketDepartment()
    {

        $department = Department::where('status',1)->get();

        if ($department->count() > 0) {
            return TicketDepartment::collection($department);
        } else {
            return response()->json(['error' => true, 'message' => 'No department found'], 200);
        }
    }

    public function users_all()
    {

        $department = User::where('status',1)->get();

        if ($department->count() > 0) {
            return UserResource::collection($department);
        } else {
            return response()->json(['error' => true, 'message' => 'No users found'], 200);
        }
    }
}
