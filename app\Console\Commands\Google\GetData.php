<?php

namespace App\Console\Commands\Google;

use App\Models\Tenant;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;

class GetData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'google:reviews';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {

        try {
            $tenants = Tenant::with('domains')->get();

            foreach ($tenants as $tenant)
            {
                foreach ($tenant->domains as $domain)
                {
                    Http::get('https://'.$domain->domain.'/webhook/google');
                }

            }
        }catch (\Exception $e)
        {
            //            Mail::raw($e->getMessage(), function ($message) {
            //                $message->to('<EMAIL>')->subject('Exception Google Get Reviews Token');
            //            });
        }

    }
}
