<?php

namespace App\Exports;

use App\Models\Tenant\CategoryLevel;
use App\Models\Tenant\Ticket;
use App\Models\Tenant\TicketCategory;
use App\Models\User;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class TicketsExport implements FromCollection, WithMapping, WithHeadings
{
    use Exportable;

    public $data;
    public $categoryLevel =[];

    public function __construct($data)
    {
        $this->data = $data;
        $this->categoryLevel = CategoryLevel::pluck('label','id')->toArray();

    }

    public function collection()
    {
        $this->data['to'] == null ? $to = now() :$to = $this->data['to'];

        if ($this->data['report_type'] == 'All')
        {
//            if ($this->data['from'] != null)
//            {
//                $tickets =  Ticket::whereBetween('created_at', [$this->data['from'], $to])->get();
//            }else
//            {
//                $tickets = Ticket::get();
//            }

            $tickets =
                Ticket::with(['ticketCustomer','ticketInfo','ticketActions'])
                    ->whereHas('ticketInfo',function ($q) use($to){$q->where('created_at', '>=',  $this->data['from'])->where('created_at', '<=',  $to);})
                    ->get();


        }
        elseif($this->data['report_type'] == 'Voice')
        {
//            if ($this->data['from'] != null)
//            {
//                $tickets =  Ticket::
//                rightJoin('ticket_infos', 'ticket_infos.ticket_id', '=', 'tickets.id')
//                    ->where(function ($query) {
//                        $query->where('ticket_infos.channel', '=', 'Voice');
//                        })
//                    ->whereBetween('ticket_infos.created_at', [$this->data['from'], $to])->get();
//            }else
//            {
//                $tickets = Ticket::rightJoin('ticket_infos', 'ticket_infos.ticket_id', '=', 'tickets.id')
//                    ->where(function ($query) {
//                        $query->where('ticket_infos.channel', '=', 'Voice');
//                    })->get();
//            }
            $tickets =
                Ticket::with(['ticketCustomer','ticketInfo','ticketActions'])
                    ->whereHas('ticketInfo',function ($q)use($to){$q->where('channel','Voice')->where('created_at', '>=',  $this->data['from'])->where('created_at', '<=',  $to);})
                    ->get();
        }
        else{
//            if ($this->data['from'] != null)
//            {
//                $tickets =  Ticket::rightJoin('ticket_infos', 'ticket_infos.ticket_id', '=', 'tickets.id')
//                    ->where(function ($query) {
//                        $query->where('ticket_infos.channel', '!=', 'Voice');
//                    })
//                    ->whereBetween('ticket_infos.created_at', [$this->data['from'], $to])->get();
//            }else
//            {
//                $tickets = Ticket::rightJoin('ticket_infos', 'ticket_infos.ticket_id', '=', 'tickets.id')
//                    ->where(function ($query) {
//                        $query->where('ticket_infos.channel', '!=', 'Voice');
//                    })->get();
//            }

            $tickets =
                Ticket::with(['ticketCustomer','ticketInfo','ticketActions'])
                    ->whereHas('ticketInfo',function ($q)use($to){$q->where('channel','!=','Voice')->where('created_at', '>=',  $this->data['from'])->where('created_at', '<=',  $to);})
                    ->get();
        }

        return $tickets;
    }

    public function map($tickets): array
    {
        $data_array = [];

//        $user  = User::whereId($tickets->ticketInfo->agent_id)->first();
//        $data_array[] =  $tickets->id;
//        $data_array[] =  $tickets->ticketInfo->channel;
//        $data_array[] =  $tickets->department_id != 0 ? $tickets->department->name : '-';
//        $data_array[] =  $tickets->ticketInfo->agent_id;
//        $data_array[] =  $user != null ? $user->first_name : '-';
//        $data_array[] =  $tickets->ticketCustomer->display_name;
//        $data_array[] =  $tickets->status;
//
//        //category logic
//        $lastAction = TicketCategory::where('ticket_action_id',$tickets->ticketCategories()->max('ticket_action_id'))->where('ticket_id',$tickets->id)->get();
//        foreach ($lastAction as $last) {$data_array[] = $last->category->name;}
//
//        $data_array[] =  $tickets->ticketInfo->comment;
//        $data_array[] =  $tickets->created_at;


        $data_array[] =  $tickets->id;
        $data_array[] =  $tickets->ticketInfo->channel;
        $data_array[] =  $tickets->ticketInfo->type;
        $data_array[] =  isset($tickets->ticketCustomer) ?$tickets->ticketCustomer->display_name : '-';
        $data_array[] =  $tickets->status;
        $data_array[] =  $tickets->ticketActions[0]->user->first_name;
        $data_array[] =  $tickets->ticketActions[0]->department->name;
        $data_array[] =  $tickets->created_at->format('M d, y H:i:s');
        $data_array[] =  $tickets->status != 'New' ?$tickets->updated_at->format('M d, y H:i:s') : '-';

        return $data_array;
    }

    public function headings(): array
    {
//        $labels = ['Ticket ID', 'Channel', 'Department', 'User ID', 'User Name', 'Customer Name', 'Status', 'Comment', 'Created At'];
//
//        $index = array_search('Comment', $labels);
//
//        foreach ($this->categoryLevel as $level)
//        {
//            array_splice($labels, $index , 0, $level);
//        }

        $labels = ['Ticket ID', 'Channel', 'Type','Customer Name', 'Status', 'Agent', 'Department', 'Created At	', 'Updated At'];
        return $labels;
    }


}
