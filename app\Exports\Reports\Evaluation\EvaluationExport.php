<?php

namespace App\Exports\Reports\Evaluation;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class EvaluationExport implements FromCollection, WithHeadings, WithMapping, ShouldAutoSize
{
    protected $records;

    public function __construct($records)
    {
        $this->records = $records;
    }

    public function collection()
    {
        return collect($this->records);
    }

    public function map($row): array
    {
        $data = [
            $row['referenceID'] ?? '-',
            $row['name'] ?? '-',
            $row['user_id'] ?? '-',
            $row['created_at'] ?? '-',
            $row['source'] ?? '-',
        ];
        return $data;
    }

    public function headings(): array
    {
        $headings = [
            'Reference ID',
            'Name',
            'User Id',
            'Date & Time',
            'Source',
        ];
        return $headings;
    }
}
