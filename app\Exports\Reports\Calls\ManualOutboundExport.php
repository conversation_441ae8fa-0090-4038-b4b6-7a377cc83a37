<?php

namespace App\Exports\Reports\Calls;

use Illuminate\Support\Str;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ManualOutboundExport implements FromCollection, WithHeadings, WithMapping
{
    protected $records;
    protected $headings;

    public function __construct($records, $headings)
    {
        $this->records = $records;
        $this->headings = $headings;
    }

    public function collection()
    {
        return $this->records;
    }

    public function headings(): array
    {
        return $this->headings;
    }

    public function map($record): array
    {
        return [
            $record->user_id ?? 'N/A',
            $record->user ? $record->user->first_name : '-',
            $record->callerIDName ?? 'N/A',
            'Outbound',
            $record->didCalled ?? 'N/A',
            $record->startedAt ?? 'N/A',
            $record->talkTime ?? 0,
            $record->wrapUpTime ?? 0,
            $record->holdTime ?? 0,
        ];
    }
} 