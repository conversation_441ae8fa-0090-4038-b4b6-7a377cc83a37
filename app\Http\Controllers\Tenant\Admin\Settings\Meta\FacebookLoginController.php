<?php

namespace App\Http\Controllers\Tenant\Admin\Settings\Meta;

use App\Http\Controllers\Controller;
use Facebook\Facebook;
use Illuminate\Http\Request;

class FacebookLoginController extends Controller
{
    public function login()
    {
        $fb = new Facebook([
            'app_id' => '***************',
            'app_secret' => '82a327c977fd62dfd2eea60f307b48f5',
            'default_graph_version' => 'v17.0',
        ]);

        $helper = $fb->getRedirectLoginHelper();
        $permissions = ['public_profile', 'pages_show_list', 'instagram_basic'];
        $loginUrl = $helper->getLoginUrl(route('admin.facebook.login'), $permissions);

        return view('tenant.admin.settings.meta.login', ['loginUrl' => $loginUrl]);
    }

    public function callback(Request $request)
    {
        $fb = new Facebook([
            'app_id' => '***************',
            'app_secret' => '82a327c977fd62dfd2eea60f307b48f5',
            'default_graph_version' => 'v17.0',
        ]);

        $helper = $fb->getRedirectLoginHelper();
        $accessToken = $helper->getAccessToken();

        if (!isset($accessToken)) {
            return redirect()->route('admin.facebook.login');
        }

        $fb->setDefaultAccessToken($accessToken);

        // Fetch user's pages
        try {
            $response = $fb->get('/me/accounts', $accessToken);
            $pages = $response->getGraphEdge()->asArray();
        } catch (\Facebook\Exceptions\FacebookResponseException $e) {
            // Handle error
        }

        // Fetch user's Instagram accounts
        try {
            $response = $fb->get('/me?fields=instagram_accounts', $accessToken);
            $user = $response->getGraphUser();
            $instagramAccounts = $user['instagram_accounts']->asArray();
        } catch (\Facebook\Exceptions\FacebookResponseException $e) {
            // Handle error
        }

        return view('tenant.admin.settings.meta.select_pages', [
            'pages' => $pages,
            'instagramAccounts' => $instagramAccounts,
        ]);
    }


}
