<?php

namespace App\Console\Commands\Ranking;

use App\Models\Tenant;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class MonthlyTarget extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'agent:monthly-rank';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'monthly-rank';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $tenants = Tenant::with('domains')->get();

            foreach ($tenants as $tenant)
            {
                foreach ($tenant->domains as $domain)
                {
                    Http::get('https://'.$domain->domain.'/ranking/monthly');
                }
//                Http::get('https://'.$tenant->domains[0]->domain.'/ranking/monthly');
            }
        }catch (\Exception $e)
        {

        }
    }
}
