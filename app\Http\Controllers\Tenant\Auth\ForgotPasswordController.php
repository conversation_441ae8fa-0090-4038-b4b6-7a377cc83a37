<?php

namespace App\Http\Controllers\Tenant\Auth;

use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Session;
use Illuminate\Foundation\Auth\SendsPasswordResetEmails;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Password;
use Illuminate\Validation\ValidationException;

class ForgotPasswordController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Password Reset Controller
    |--------------------------------------------------------------------------
    |
    | This controller is responsible for handling password reset emails and
    | includes a trait which assists in sending these notifications from
    | your application to your users. Feel free to explore this trait.
    |
    */

    use SendsPasswordResetEmails;

    public function showLinkRequestForm()
    {

        return view('tenant.auth.passwords.email');
    }

    public function sendResetLinkEmail(Request $request)
    {
        // Validate email input
        $this->validateEmail($request);

        // Send the password reset link
        $response = $this->broker()->sendResetLink(
            $this->credentials($request)
        );

        // Return a generic response regardless of success or failure
        return $request->wantsJson()
            ? response()->json([
                'message' => 'If the email address exists in our system, you will receive a password reset link shortly.'
            ], 200)
            : back()->with('status', 'If the email address exists in our system, you will receive a password reset link shortly.');
    }
}
