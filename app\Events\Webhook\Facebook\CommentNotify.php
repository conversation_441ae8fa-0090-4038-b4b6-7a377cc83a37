<?php

namespace App\Events\Webhook\Facebook;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CommentNotify implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $tenant_id;
    public $comment;

    public function __construct($tenant_id, $comment)
    {
        $this->tenant_id    =   $tenant_id;
        $this->comment      =   $comment;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastWith()
    {
        return [
            'tenant_id'     =>$this->tenant_id,
            'content'       =>$this->comment,

        ];
    }

    public function broadcastOn()
    {
        return new PrivateChannel('Comment.'.$this->tenant_id);
    }
}
