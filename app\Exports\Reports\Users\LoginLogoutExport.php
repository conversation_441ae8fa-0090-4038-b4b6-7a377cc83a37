<?php

namespace App\Exports\Reports\Users;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class LoginLogoutExport implements FromCollection, WithHeadings, WithMapping, ShouldAutoSize
{
    protected $loginLogoutData;
    protected $auxTypes;

    public function __construct($loginLogoutData, $auxTypes = [])
    {
        $this->loginLogoutData = $loginLogoutData;
        $this->auxTypes = $auxTypes;
    }

    public function collection()
    {
        return collect($this->loginLogoutData);
    }

    public function map($row): array
    {
        $data = [
            $row['user_id'] ?? '',
            $row['name'] ?? '',
            $row['date'] ?? '',
        ];
        
        foreach ($this->auxTypes as $auxType) {
            $data[] = $row['AUX'][$auxType] ?? '00:00:00';
        }
        
        return $data;
    }

    public function headings(): array
    {
        $headings = [
            'User ID',
            'Name',
            'Date',
        ];
        
        foreach ($this->auxTypes as $auxType) {
            $headings[] = $auxType;
        }
        
        return $headings;
    }
}
