<?php

namespace App\Console\Commands\LinkedIn;

use App\Models\Tenant;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class RefreshToken extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'linkedin:refresh-token';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'linkedin refresh token';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $tenants = Tenant::with('domains')->get();

            foreach ($tenants as $tenant)
            {
                foreach ($tenant->domains as $domain)
                {
                    $response = Http::get('https://'.$domain->domain.'/webhook/linkedin/refresh');
                }

            }
        }catch (\Exception $e)
        {
//            Mail::raw($e->getMessage(), function ($message) {
//                $message->to('<EMAIL>')->subject('Exception Google Refresh Token | google:refresh-token');
//            });
        }

    }
}
