<?php

namespace App\Exports\Reports\Calls;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class VoiceReportExport implements FromCollection, WithHeadings, WithMapping
{
    public $records;

    public function __construct($records)
    {
        $this->records = $records; 
    }

    public function collection()
    {
        return $this->records;
    }

    public function map($record): array
    {
        return [
            $record->startedAt ? $record->startedAt->toDateTimeString() : 'N/A',
            $record->callerIDNumber ?? 'N/A',
            $record->duration ?? 'N/A',
            $record->result ?? 'N/A',
            $record->user ? (($record->user->first_name ?? '') . ' ' . ($record->user->last_name ?? '')) : 'N/A',
            $record->queue_name ?? 'N/A',
            $record->talkTime ?? 'N/A',
            $record->answeredAt ? $record->answeredAt->toDateTimeString() : 'N/A',
            $record->surveyRating ?? 'N/A',
            $record->holdTime ?? 'N/A',
        ];
    }

    public function headings(): array
    {
        return [
            'Start Date',
            'Caller Number',
            'Total Duration',
            'Call Result',
            'Agent Full Name',
            'Queue',
            'Talk Time',
            'Answered At',
            'Survey Rating',
            'On-Hold Duration',
        ];
    }
} 