<?php

namespace App\Exports;

use App\Models\Tenant\Resource;
use Maatwebsite\Excel\Concerns\Exportable;
use App\Models\Tenant\ResourceInfo;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithMapping;

class ChatExport implements FromCollection, WithMapping, WithHeadings
{
    use Exportable;

    public $data;

    public function __construct($data)
    {
        $this->data = $data;
    }


    public function collection()
    {
        $this->data['to'] == null ? $to = now() :$to = $this->data['to'];

//        if ($this->data['report_type'] == 'All')
//        {
//            if ($this->data['from'] != null)
//            {
//                $resource_info = ResourceInfo::where('channel','!=','Livechat')->where('channel','!=','Whatsapp')->whereBetween('created_at', [$this->data['from'], $to])->get();
//            }else{
//                $resource_info = ResourceInfo::where('channel','!=','Livechat')->where('channel','!=','Whatsapp')->get();
//            }
//        }else{
//            if ($this->data['from'] != null)
//            {
//                $resource_info = ResourceInfo::where('channel','!=','Livechat')->where('channel','!=','Whatsapp')->where('channel',$this->data['report_type'])->whereBetween('created_at', [$this->data['from'], $to])->get();
//            }else{
//                $resource_info = ResourceInfo::where('channel','!=','Livechat')->where('channel','!=','Whatsapp')->where('channel',$this->data['report_type'])->get();
//            }
//        }


        if ($this->data['report_type'] == 'All') {
            $resource_info = Resource::with(['resourceInfo', 'user'])
                ->whereHas('resourceInfo', function ($q) use ($to) {
                    $q->with('customer')->where(function ($whereData)
                    {
                        $whereData->where('channel', 'Whatsapp')
                        ->orWhere('channel', 'Livechat');
                    })
                        ->where('created_at', '>=', $this->data['from'])
                        ->where('created_at', '<=', $to);
                })
                ->get();
        }else
        {
            $resource_info = Resource::with(['resourceInfo', 'user'])
                ->whereHas('resourceInfo', function ($q) use ($to) {
                    $q->with('customer')->where('channel', $this->data['report_type'])
                        ->where('created_at', '>=', $this->data['from'])
                        ->where('created_at', '<=', $to);
                })
                ->get();
        }
        return $resource_info;
    }

    public function map($resource_info): array
    {

        $data_array = [];
//        $user  = User::whereId($resource_info->resource->user_id)->first();
//
//        $data_array[]=$resource_info->resource_id;
//        $data_array[]=$resource_info->channel;
//        $data_array[]=$resource_info->type;
//        $data_array[]=$resource_info->account_name;
//        $data_array[]=$resource_info->customer->display_name;
//
//        $data_array[]=$resource_info->resource->status;
//        $data_array[]=$user != null ? $resource_info->resource->user_id : '-';
//        $data_array[]=$user != null ? $user->first_name : '-' ;
//
//        $data_array[]=$resource_info->first_response_time;
//
//        $data_array[]=$resource_info->created_at;
//
//
//        foreach ($resource_info->resource->resourceActions  as $resourceAction) {
//            if ($resourceAction->action == 'Resolved')
//            {
//                $data_array[]=$resourceAction->created_at;
//
//            }elseif($resourceAction->action == 'Closed')
//            {
//                $data_array[]=$resourceAction->created_at;
//            }
//
//        }

        $data_array[]=$resource_info->id;
        $data_array[]=$resource_info->resourceInfo->channel;
//        $data_array[]=$resource_info->resourceInfo->type;
        $data_array[]=$resource_info->resourceInfo->account_name;
        $data_array[]=isset($resource_info->resourceInfo->customer) ? $resource_info->resourceInfo->customer->display_name : '-';
        $data_array[]=$resource_info->status;
        $data_array[]=$resource_info->resourceInfo->first_response_time ? $resource_info->resourceInfo->first_response_time : '-';
        $data_array[]=$resource_info->user_id ? $resource_info->user->first_name : '-';
        $data_array[]=$resource_info->created_at->format('M d, y H:i:s');
        $data_array[]=$resource_info->status != 'New' ?$resource_info->updated_at->format('M d, y H:i:s') : '-';

        return $data_array;
    }

    public function headings(): array
    {
//        return ['Resource ID', 'Channel', 'Type', 'Account', 'Customer Name', 'Status', 'Username', 'Name','First Response Time', 'Created At', 'Resolved At', 'Closed At'];
        return ['Source ID', 'Channel',  'Account', 'Customer Name', 'Status','FRT', 'Agent', 'Created At', 'Updated At'];
    }
}
