<?php

namespace App\Http\Controllers\Tenant\Admin\WFM;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class MinaController extends Controller
{
    public function index()
    {
        return view('tenant.admin.wfm.index');
    }
    public function work_day()
    {
        return view('tenant.admin.wfm.work_day');
    }
    public function status()
    {
        return view('tenant.admin.wfm.status');
    }
    public function skill()
    {
        return view('tenant.admin.wfm.skill');
    }
    public function skill_group()
    {
        return view('tenant.admin.wfm.skill_group');
    }
    public function shift_type()
    {
        return view('tenant.admin.wfm.shift_type');
    }
    public function break()
    {
        return view('tenant.admin.wfm.break');
    }
    public function activation()
    {
        return view('tenant.admin.wfm.activation');
    }

}
