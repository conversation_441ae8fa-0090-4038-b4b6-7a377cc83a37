<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class EvaluationExport implements FromCollection, WithHeadings
{
    /**
    * @return \Illuminate\Support\Collection
    */
    use Exportable;

    public $data;
    public function __construct($data)
    {
        $this->data = $data;

    }

    public function collection()
    {
        $result  =  $this->data;

        return $result->map(function ($data) {

            return [
                'Reference ID'                  => $data['referenceID'] ?? null,
                'Name'                          => $data['name'] ?? null,
                'UserID'                        => $data['user_id'] ?? null,
                'Evaluation Date'               => $data['date'] ?? null,
                'Source'                        => $data['source'] ?? null,
                'Year'                          => $data['year'] ?? null,
                'Month'                         => $data['month'] ?? null,
                'Week'                          => $data['week'] ?? null,
                'Comment'                       => $data['commentEvaluation'] ?? null,
                'Total Weight Points'           => $data['total_weight_points'] ?? null,
                'Total Possible Weighted Points'=> $data['total_possible_weighted_points'] ?? null,
                'Total Score'                   => $data['total_score'] ?? null,
                'Total Possible Score'          => $data['total_possible_score'] ?? null,
                'Quality Percentage'            => $data['quality_percentage'] ?? null,
                'Extra Field One'   => isset($data['extra_field_one']) ? (json_decode($data['extra_field_one'], true)['label'] ?? null) . ' => ' . (json_decode($data['extra_field_one'], true)['value'] ?? null) : null,
                'Extra Field Two'   => isset($data['extra_field_tow']) ? (json_decode($data['extra_field_tow'], true)['label'] ?? null) . ' => ' . (json_decode($data['extra_field_tow'], true)['value'] ?? null) : null,
                'Extra Field Three' => isset($data['extra_field_three']) ? (json_decode($data['extra_field_three'], true)['label'] ?? null) . ' => ' . (json_decode($data['extra_field_three'], true)['value'] ?? null) : null,
                'Submit Date'       => $data['created_at'] ?? null,
                ];


        });

    }
    public function headings(): array
    {
        return [
            'Reference ID',
            'Name',
            'UserID',
            'Evaluation Date',
            'Source',
            'Year' ,
            'Month',
            'Week',
            'Comment',
            'Total Weight Points',
            'Total Possible Weighted Points',
            'Total Score',
            'Total Possible Score',
            'Quality Percentage',
            'Extra Field One',
            'Extra Field Tow',
            'Extra Field Three',
            'Sumbit Date',
        ];


    }



}
