<?php

namespace App\Http\Controllers\Tenant\Admin\Settings;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ChatbotController extends Controller
{

    public function index()
    {
        return view('tenant.admin.chatbot.index');
    }

    public function bots()
    {
        return view('tenant.admin.chatbot.bots.index');
    }

    public function flow()
    {
        return view('tenant.admin.chatbot.chatbot.index');
    }
    public function botlevels()
    {
        return view('tenant.admin.chatbot.botlevels.index');
    }
    public function botkeys()
    {
        return view('tenant.admin.chatbot.botkeys.index');
    }
}
