<?php

namespace App\Exports\Reports\Rating;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class CsatExport implements FromCollection, WithHeadings, WithMapping, ShouldAutoSize
{
    protected $records;
    protected $source;

    public function __construct($records, $source)
    {
        $this->records = $records;
        $this->source = $source;
    }

    public function collection()
    {
        return collect($this->records);
    }

    public function map($row): array
    {
        Log::info('Mapping row data', ['row' => $row]);
        if ($this->source == 'Avg') {
            return [
                $row['channel'] ?? '-',
                // CHANGE: Use array syntax and check for the nested user array
                ($row['user_id'] && !empty($row['user'])) ? $row['user']['first_name'] . " " . $row['user']['last_name'] : '-',
                $row['account_name'] ?? '-',
                number_format($row['average_satisfaction'] ?? 0, 2),
            ];
        } elseif ($this->source == 'Questions') {
            // Get user name from the array
            $userName = '-';
            if ($row['user_id'] && !empty($row['user'])) {
                $userName = $row['user']['first_name'] . " " . $row['user']['last_name'];
            }

            // Get questions data from the array
            $questionsData = '';
            if (!empty($row['csat'])) {
                $questions = [];
                foreach ($row['csat'] as $data) {
                    $questionText = 'N/A';
                    $value = $data['value'] ?? 'N/A';

                    // Get question text from the nested array
                    if (!empty($data['question'])) {
                        $questionText = $data['question']['question'] ?? 'N/A';
                    }
                    
                    $questions[] = $questionText . ': ' . $value;
                }
                $questionsData = implode(' | ', $questions);
            }

            return [
                $row['id'] ?? '-',
                $row['channel'] ?? '-', // This will now work!
                $userName,
                number_format($row['average_satisfaction'] ?? $row['satisfaction'] ?? 0, 2),
                $questionsData,
            ];
        } elseif ($this->source == 'Conversation') {
            return [
                $row['id'] ?? '-',
                $row['channel'] ?? '-',
                $row['type'] ?? '-',
                // CHANGE: Use array syntax and check for the nested user array
                ($row['user_id'] && !empty($row['user'])) ? $row['user']['first_name'] . " " . $row['user']['last_name'] : '-',
                $row['account_name'] ?? '-',
                $row['username'] ?? '-',
                $row['display_name'] ?? '-',
                // CHANGE: The date is now a string, so we parse it to format it
                !empty($row['created_at']) ? Carbon::parse($row['created_at'])->format('Y-m-d H:i:s') : '-',
                number_format($row['average_satisfaction'] ?? 0, 2),
            ];
        }

        return [];
    }

    public function headings(): array
    {
        if ($this->source == 'Avg') {
            return [
                'Channel',
                'Agent',
                'Account',
                'Rate */5',
            ];
        } elseif ($this->source == 'Questions') {
            return [
                'Resource ID',
                'Channel',
                'Agent',
                'Rate */5',
                'Questions',
            ];
        } elseif ($this->source == 'Conversation') {
            return [
                'Resource ID',
                'Channel',
                'Type',
                'Agent',
                'Account',
                'Customer Account',
                'Customer Name',
                'Date',
                'Rate */5',
            ];
        }

        return [];
    }
}