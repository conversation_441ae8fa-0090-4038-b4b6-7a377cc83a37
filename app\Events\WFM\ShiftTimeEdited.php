<?php

namespace App\Events\WFM;

use Illuminate\Support\Facades\Log;
use Illuminate\Broadcasting\Channel;
use Illuminate\Queue\SerializesModels;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;

class ShiftTimeEdited
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $shiftId;

    public function __construct($shiftId)
    {
        $this->shiftId = $shiftId;
    }
}
