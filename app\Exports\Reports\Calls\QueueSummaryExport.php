<?php

namespace App\Exports\Reports\Calls;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class QueueSummaryExport implements FromCollection, WithHeadings, WithMapping
{
    protected $records;
    protected $headings;

    public function __construct($records, $headings)
    {
        $this->records = $records;
        $this->headings = $headings;
    }

    public function collection()
    {
        return $this->records;
    }

    public function headings(): array
    {
        return $this->headings;
    }

    public function map($row): array
{
    return [
        $row['queue_name'],
        $row['interaction'],
        $row['day'],
        $row['offered'] === 0 ? '0' : $row['offered'],
        $row['accepted'] === 0 ? '0' : $row['accepted'],
        $row['not_accepted'] === 0 ? '0' : $row['not_accepted'],
        $row['not_accepted'] === 0 ? '0' : $row['not_accepted'], // Rejected
        $row['handle_time'] === 0 ? '00:00:00' : gmdate('H:i:s', $row['handle_time']),
        $row['avg_handle_time'] === 0 ? '00:00:00' : gmdate('H:i:s', $row['avg_handle_time']),
        $row['invite_time'] === 0 ? '00:00:00' : gmdate('H:i:s', $row['invite_time']),
        $row['max_invite'] === 0 ? '00:00:00' : gmdate('H:i:s', $row['max_invite']),
        $row['avg_invite_time'] === 0 ? '00:00:00' : gmdate('H:i:s', $row['avg_invite_time']),
        $row['total_engage'] === 0 ? '00:00:00' : gmdate('H:i:s', $row['total_engage']),
        $row['total_hold'] === 0 ? '00:00:00' : gmdate('H:i:s', $row['total_hold']),
        $row['total_wrap'] === 0 ? '00:00:00' : gmdate('H:i:s', $row['total_wrap']),
        $row['avg_engage'] === 0 ? '00:00:00' : gmdate('H:i:s', $row['avg_engage']),
        $row['avg_hold'] === 0 ? '00:00:00' : gmdate('H:i:s', $row['avg_hold']),
        $row['avg_wrap'] === 0 ? '00:00:00' : gmdate('H:i:s', $row['avg_wrap']),
    ];
}

} 