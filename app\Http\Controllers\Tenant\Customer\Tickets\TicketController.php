<?php

namespace App\Http\Controllers\Tenant\Customer\Tickets;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Ticket;
use Illuminate\Http\Request;

class TicketController extends Controller
{
    public function index(){

        $counterTicket = Ticket::whereHas('ticketCustomer',function($q){$q->where('username',  auth()->user()->email);})->count();

//        dd($counterTicket);
        return view('tenant.customer.tickets.index', compact('counterTicket'));
    }


    public function create(){

        return view('tenant.customer.tickets.create');
    }
}
