<?php

namespace App\Console\Commands\Ranking;

use App\Models\Tenant;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class WeeklyTarget extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'agent:weekly-rank';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'weekly-rank';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $tenants = Tenant::with('domains')->get();

            foreach ($tenants as $tenant)
            {
                foreach ($tenant->domains as $domain)
                {
                    Http::get('https://'.$domain->domain.'/ranking/weekly');
                }
//                Http::get('https://'.$tenant->domains[0]->domain.'/ranking/weekly');
            }
        }catch (\Exception $e)
        {

        }
    }
}
