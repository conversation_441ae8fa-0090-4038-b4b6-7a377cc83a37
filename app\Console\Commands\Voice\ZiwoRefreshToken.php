<?php

namespace App\Console\Commands\Voice;

use Illuminate\Console\Command;

class ZiwoRefreshToken extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:ziwo-refresh-token';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $tenants = Tenant::with('domains')->get();

            foreach ($tenants as $tenant)
            {
                foreach ($tenant->domains as $domain)
                {
                    Http::get('https://'.$domain->domain.'/voice/refresh-token');
//                    Http::get('https://'.$domain->domain.'/escalation');
                }

            }
        }catch (\Exception $e)
        {

        }
    }
}
