<?php

namespace App\Exports;

use App\Models\Tenant\Ticket;
use App\Models\Tenant\User;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use App\Models\Tenant\ResourceInfo;

class CallsExport implements FromQuery
{
    use Exportable;
    protected $data;


    public function __construct($data)
    {
        $this->data = $data;
    }


    public function query()
    {
        
    }
}
