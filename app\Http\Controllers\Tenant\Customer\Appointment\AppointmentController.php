<?php

namespace App\Http\Controllers\Tenant\Customer\Appointment;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class AppointmentController extends Controller
{
    public function index(){

        return view('tenant.customer.appointment.index');
    }


    public function create(){

        return view('tenant.customer.appointment.create');
    }
    public function edit($id){

        return view('tenant.customer.appointment.edit',compact('id'));
    }
}
