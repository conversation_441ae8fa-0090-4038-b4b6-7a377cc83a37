<?php

namespace App\Exports;

use Carbon\Carbon;
use Carbon\CarbonPeriod;
use App\Models\Tenant\User;
use App\Models\Tenant\WfmSkill;
use Illuminate\Support\Facades\Request;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;

class EveryDayMonitoringExport implements FromCollection, WithHeadings
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public $defaultAccount;
    public $from;
    public $to;
    public $datePeriod;


    public function collection()
    {
        $this->from = Carbon::now();  
        $this->to =  Carbon::parse($this->from)->addDays(7)->format('Y-m-d');
        $this->from = Carbon::parse($this->from)->format('Y-m-d');  


        $result= User::query();

        $result->with('WfmSchedule' ,'wfmUserSkill')->whereHas('roles',function ($q){$q->where('allowed_route','agent');});
        if ($this->from != null && $this->to != null) {
            $result->whereHas('WfmSchedule', function ($subquery)  {
                $subquery->whereBetween('date', [$this->from, $this->to]);
            })->whereHas('roles',function ($q){$q->where('allowed_route','agent');})->get();
        }

        $now = Carbon::now();
        $startDate = $now->clone()->startOfDay();
        $endDate = $now->clone()->addDays(14)->endOfDay();
        
        $this->datePeriod =  collect(CarbonPeriod::create($startDate, $endDate)->toArray())
          ->map(function($eachCarbonDate){
            return $eachCarbonDate->format('Y-m-d');
          });



        return $result->get()->map(function ($item) {

            $date = ['Full Name' => $item->first_name ,];



            foreach($item->WfmSchedule as $Schedule){

     
                foreach($this->datePeriod as $eachFormattedDate){
             
                    if($eachFormattedDate == Carbon::parse($Schedule->date)->format('Y-m-d')){

                     
                        if($Schedule->wfm_shift_id== null){

                            array_push($date, $Schedule->workDay->type);
                            
                        }elseif($Schedule->shift?->from != null){
                            array_push($date,' '.Carbon::parse($Schedule->shift?->from)->format('H:i').
                            ' - '.Carbon::parse($Schedule->shift?->to)->format('H:i').'');
                        }
                        
                    }
                        

                }

            }
            return $date;
        }

        
    );
        
    }


    


    public function headings(): array
    {
        $now = Carbon::now();
        $startDate = $now->clone()->startOfDay();
        $endDate = $now->clone()->addDays(14)->endOfDay();
        
        $this->datePeriod =  collect(CarbonPeriod::create($startDate, $endDate)->toArray())
          ->map(function($eachCarbonDate){
            return $eachCarbonDate->format('Y-m-d');
          });

        $date = ['Name',];
                    
        if($this->datePeriod != null){

            foreach( $this->datePeriod as $eachFormattedDate){
                array_push($date, $eachFormattedDate);
            }
            
        
        }

       return $date;
      
      
    }
}
