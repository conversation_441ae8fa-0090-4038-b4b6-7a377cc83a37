<?php

namespace App\Http\Controllers\Tenant\API;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Role;
use App\Models\Tenant\User;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class CreateAdminTenantController extends Controller
{
    public function __invoke(Request $request)
    {
        $role = Role::whereName('admin')->first();

        $admin = User::create([
            'first_name'        =>  $request->first_name,
            'last_name'         =>  $request->last_name,
            'username'          =>  $request->username,
            'email'             =>  $request->admin_email,
            'email_verified_at' =>  now(),
            'mobile'            =>  $request->mobile,
            'user_image'        =>  'avatar.svg',
            'password'          =>  bcrypt($request->password),
            'status'            =>  1,
            'remember_token'    => '',
        ]);

        $admin->attachRole($role);


        return response()->json($admin);
    }


}
