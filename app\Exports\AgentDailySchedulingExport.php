<?php

namespace App\Exports;

use Carbon\Carbon;
use Carbon\CarbonPeriod;
use App\Models\Tenant\User;
use App\Models\Tenant\WfmBreak;
use App\Models\Tenant\WfmSkill;
use Illuminate\Support\Facades\Request;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;

class AgentDailySchedulingExport implements FromCollection, WithHeadings
{
    /**
    * @return \Illuminate\Support\Collection
    */

    
    public $defaultAccount;
    public $datePeriod;

    public function collection()
    {



       
        $result  =  User::query()->with('WfmSchedule' ,'wfmUserSkill')->whereHas('roles',function ($q){$q->where('allowed_route','agent');})->get();
        $url = Request::url();
        $this->defaultAccount = explode('.', parse_url($url, PHP_URL_HOST))[0];


        $now = Carbon::now();
        $startDate = $now->clone()->startOfDay();
        $endDate = $now->clone()->addDays(14)->endOfDay();
        $this->datePeriod =  collect(CarbonPeriod::create($startDate, $endDate)->toArray())
        ->map(function($eachCarbonDate){
          return $eachCarbonDate->format('Y-m-d');
        });

        return $result->map(function ($item) {

          $data = [  'Name' => $item->first_name,
          'User Name' => $item->username,
          'Account' => $this->defaultAccount,
          'Skill Name' => $item->wfmUserSkill->WfmSkill->skill_name,
        ];

   

        foreach( $this->datePeriod as $eachFormattedDate){

           $Schedule = $item->WfmSchedule->where('date' ,Carbon::parse($eachFormattedDate))->first();
           
            if( $eachFormattedDate == \Carbon\Carbon::parse($Schedule->date?? "")->format('Y-m-d') ){
       
                     
                if($Schedule->wfm_shift_id == null){
                    array_push( $data,'No Shift'); 
                }
                
                else{
                    array_push($data,' '.Carbon::parse($Schedule->shift->from ?? "")->format('H:i').
                    ' - '.Carbon::parse($Schedule->shift->to ?? "")->format('H:i').'');               
                }
            }
     
            else{
                array_push( $data,'-');
            }

        }

             return $data;

      
        });
        
    }

    public function headings(): array
    {

        $now = Carbon::now();
        $startDate = $now->clone()->startOfDay();
        $endDate = $now->clone()->addDays(14)->endOfDay();
        
        $this->datePeriod =  collect(CarbonPeriod::create($startDate, $endDate)->toArray())
          ->map(function($eachCarbonDate){
            return $eachCarbonDate->format('Y-m-d');
          });

          return [
          'Name', 
          'User Name',
          'Account', 
          'Skill' ,
        ];
                    
        if($this->datePeriod != null){

            foreach( $this->datePeriod as $eachFormattedDate){
                array_push($date, $eachFormattedDate);
            }
            
        
        }
       return $date;
      
    }
}
