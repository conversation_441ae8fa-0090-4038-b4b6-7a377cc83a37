<?php

namespace App\Helper;

use Carbon\Carbon;
use App\Models\Tenant\WorkType;
use App\Models\Tenant\WorkHoliday;
use App\Models\Tenant\Distribution;

class SLAHelper
{
    public static function calculate($ticket): ?array
    {
        if (!$ticket) return null;

        $category = $ticket->ticketCategories()->latest('id')->first();
        $distribution = $category ? Distribution::where('category_id', $category->category_id)->first() : null;
        if (!$distribution) return null;

        $startTime = $ticket->status === 'New' ? Carbon::parse($ticket->created_at) : Carbon::parse($ticket->updated_at);
        $endTime = Carbon::now();

        $workDay = WorkType::where('status', 1)->with('days.shifts')->first();
        $holidays = WorkHoliday::where('status', 1)->get(['date', 'date_to']);
        if (!$workDay || $workDay->days->isEmpty()) return null;

        // Build shift map by day name
        $shiftsMap = [];
        foreach ($workDay->days as $day) {
            foreach ($day->shifts as $shift) {
                $shiftsMap[$day->day][] = [
                    'start' => Carbon::parse($shift->start_time),
                    'end'   => Carbon::parse($shift->end_time),
                ];
            }
        }

        $slaDetail = $distribution->slaGroup?->slaDetails?->where('priority', $ticket->ticketInfo->priority)->first();
        if (!$slaDetail) return null;

        $slaTime = $ticket->status === 'New' ? $slaDetail->first_response_time : $slaDetail->every_response_time;
        $slaType = $ticket->status === 'New' ? $slaDetail->first_response_type : $slaDetail->every_response_type;
        $slaInMinutes = match ($slaType) {
            'minutes' => $slaTime,
            'hours'   => $slaTime * 60,
            'days'    => $slaTime * 1440,
            default   => 0,
        };

        $totalWorkedMinutes = 0;
        $current = clone $startTime;

        while ($current->lt($endTime)) {
            $dayName = $current->format('l');
            $dateStr = $current->format('Y-m-d');

            $isHoliday = $holidays->contains(function ($holiday) use ($dateStr) {
                return $dateStr >= $holiday['date'] && $dateStr <= $holiday['date_to'];
            });

            if (!$isHoliday && isset($shiftsMap[$dayName])) {
                foreach ($shiftsMap[$dayName] as $shift) {
                    $shiftStart = clone $shift['start'];
                    $shiftEnd = clone $shift['end'];

                    $shiftStart->setDate($current->year, $current->month, $current->day);
                    $shiftEnd->setDate($current->year, $current->month, $current->day);
                    if ($shiftEnd->lt($shiftStart)) {
                        $shiftEnd->addDay();
                    }

                    $effectiveStart = $current->greaterThan($shiftStart) ? $current : $shiftStart;
                    $effectiveEnd = $shiftEnd->greaterThan($endTime) ? $endTime : $shiftEnd;

                    if ($effectiveStart->lt($effectiveEnd)) {
                        $totalWorkedMinutes += $effectiveStart->diffInMinutes($effectiveEnd);
                    }
                }
            }

            $current->addDay()->startOfDay();
        }

        $isOverdue = $totalWorkedMinutes > $slaInMinutes;

        $departmentAction = $ticket->ticketActions()
            ->where('department_id', '!=', 0)
            ->latest('id')
            ->first();

        $departmentName = $departmentAction?->department?->name ?? '-';

        return [
            'is_overdue'       => $isOverdue,
            'worked_minutes'   => $totalWorkedMinutes,
            'sla_minutes'      => $slaInMinutes,
            'overdue_by'       => $isOverdue ? $totalWorkedMinutes - $slaInMinutes : 0,
            'remaining_minutes'=> !$isOverdue ? $slaInMinutes - $totalWorkedMinutes : 0,
            'department'       => $departmentName
        ];
    }
}
