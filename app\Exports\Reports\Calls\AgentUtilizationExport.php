<?php

namespace App\Exports\Reports\Calls;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class AgentUtilizationExport implements FromCollection, WithHeadings, WithMapping
{
    public $records;

    public function __construct($records)
    {
        $this->records = $records;
    }

    public function collection()
    {
        return $this->records;
    }

    public function map($row): array
{
    return [
        $row['agent_name'],
        $row['interaction'],
        $row['day'],
        $row['offered'] === 0 ? '0' : $row['offered'],
        $row['accepted'] === 0 ? '0' : $row['accepted'],
        $row['not_accepted'] === 0 ? '0' : $row['not_accepted'],
        $row['not_accepted'] === 0 ? '0' : $row['not_accepted'], // Rejected
        $row['handle_time'] === 0 ? '00:00:00' : gmdate('H:i:s', $row['handle_time']),
        $row['avg_handle_time'] === 0 ? '00:00:00' : gmdate('H:i:s', $row['avg_handle_time']),
        $row['invite_time'] === 0 ? '00:00:00' : gmdate('H:i:s', $row['invite_time']),
        $row['max_invite'] === 0 ? '00:00:00' : gmdate('H:i:s', $row['max_invite']),
        $row['avg_invite_time'] === 0 ? '00:00:00' : gmdate('H:i:s', $row['avg_invite_time']),
        $row['total_engage'] === 0 ? '00:00:00' : gmdate('H:i:s', $row['total_engage']),
        $row['total_hold'] === 0 ? '00:00:00' : gmdate('H:i:s', $row['total_hold']),
        $row['total_wrap'] === 0 ? '00:00:00' : gmdate('H:i:s', $row['total_wrap']),
        $row['avg_engage'] === 0 ? '00:00:00' : gmdate('H:i:s', $row['avg_engage']),
        $row['avg_hold'] === 0 ? '00:00:00' : gmdate('H:i:s', $row['avg_hold']),
        $row['avg_wrap'] === 0 ? '00:00:00' : gmdate('H:i:s', $row['avg_wrap']),
    ];
}


    public function headings(): array
    {
        return [
            'Agent', 'Interaction', 'Day',
            'Offered', 'Accepted', 'Not Accepted', 'Rejected',
            'Handle Time', 'AVG Handle Time',
            'Invite Time', 'Max Invite', 'Avg Invite Time',
            'Total Engage', 'Total Hold', 'Total Wrap',
            'Avg Engage', 'Avg Hold', 'Avg Wrap'
        ];
    }
} 