<?php

namespace App\Http\Controllers\Tenant\Admin\Integrations;

use App\Http\Controllers\Controller;
//use Facebook\Facebook;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Laravel\Socialite\Facades\Socialite;

class FacebookController extends Controller
{

    public function redirect(Request $request)
    {
        return Socialite::driver('facebook')
            ->scopes(['pages_manage_engagement', 'pages_manage_metadata']) // Add necessary permissions
            ->redirect();
    }

    public function callback(Request $request){

        $user = Socialite::driver('facebook')->user();

        dd($user);
//        $user = $request->createOrGetUser(Socialite::driver('facebook')->user());
//
//        auth()->login($user);

    }

    public function index()
    {
        return view('tenant.admin.integrations.facebook.index');
    }


//    protected $fb;
//
//    public function __construct()
//    {
//        $this->fb = new Facebook([
//            'app_id' => config('services.facebook.client_id'),
//            'app_secret' => config('services.facebook.client_secret'),
//            'default_graph_version' => 'v17.0',
//        ]);
//    }
//
//    public function redirectToFacebook()
//    {
//        $helper = $this->fb->getRedirectLoginHelper();
////        $permissions = [
////            'pages_manage_posts',
////            'pages_manage_cta',
////            'pages_manage_messaging',
////            'pages_manage_metadata',
////            'pages_read_engagement',
////            'pages_read_engagement',
////            'pages_read_user_content',
////            'pages_manage_engagement',
////            'instagram_basic',
////            'instagram_manage_comments',
////            'instagram_manage_messages',
////
////        ];
//
//        $permissions = ['pages_manage_posts', 'pages_read_engagement', 'pages_manage_engagement','pages_manage_metadata','pages_messaging','instagram_basic','instagram_manage_messages',];
//        // Optional permissions
//        $loginUrl = $helper->getLoginUrl(route('facebook.callback'), $permissions);
//
//
//        return redirect()->to($loginUrl);
//    }
//
//    public function handleFacebookCallback(Request $request)
//    {
//
//        $helper = $this->fb->getRedirectLoginHelper();
//
//        Storage::disk('local')->put('handleFacebookCallback1.txt', json_encode($helper));
//        Storage::disk('local')->put('handleFacebookCallback2.txt', json_encode($request->all()));
////        Storage::disk('local')->put('handleFacebookCallback3.txt', json_encode($helper->getAccessToken()));
////        try {
////            $accessToken = $helper->getAccessToken();
////
////        } catch (\Exception $e) {
////            return redirect('/')->withErrors(['msg' => 'Facebook API Error: ' . $e->getMessage()]);
////        }
////
////        if (!isset($accessToken)) {
////            return redirect('/')->withErrors(['msg' => 'No access token received.']);
////        }
//
//        // Store the access token in the database
////        $user = auth()->user();
////        $user->facebook_access_token = $accessToken->getValue();
////        $user->save();
//
//        dd($helper,$request->all());
//        // Redirect or display a success message
//        return redirect('/')->with('success', 'Facebook account connected.');
//    }
}
