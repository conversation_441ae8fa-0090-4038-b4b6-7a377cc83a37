<?php

namespace App\Bootpress;

use App\Mail\TestReply;
use Illuminate\Support\Facades\Facade;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;

class Reply extends Facade
{
    public static function Reply($botName,$message, $user,$lang ="English")
  {
//        $response = Http::withHeaders([
//            'Content-Type' => 'application/json',
//            'Accept' => 'application/json',
//            'Authorization' => 'Bearer Bearer '.env('BOOTPRESS_TOKEN'),
//        ])->post('https://extbot01.extensyaai.com/api/v1/bots/'.$botName.'/converse/'.$user, [
//            'type' => 'text',
//            'text' => "{'lang':$lang,'text':$message}",
//        ]);

        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'Authorization' => 'Bearer Bearer '.env('BOOTPRESS_TOKEN'),
        ])->post('https://extbot02.extensyaai.com/api/v1/bots/'.$botName.'/converse/'.$user, [
            'type' => 'text',
            'text' => $message,
        ]);
        $statusCode = $response->status();

        if ($statusCode != 200){
            // Mail::to(['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>'])
            //     ->send(new TestReply('ChatBot Have Errors',$response,[]));

            // $response ='{
            //                 "responses": [
            //                     {
            //                         "type": "text",
            //                         "workflow": {},
            //                         "text": "سيتم توصيلك بأحد وكلائنا قريبًا، شكرًا لك على سعة صدرك.",
            //                         "variations": [
            //                             "سيتم توصيلك بأحد وكلائنا قريبًا، شكرًا لك على سعة صدرك."
            //                         ]
            //                     }
            //                 ]
            //             }';

            // return $response;

        }else{
            return $response;
        }

  }

    public static function ReplyV2($botName,$message, $user)
    {

        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'Authorization' => 'Bearer Bearer '.env('BOOTPRESS_TOKEN'),
        ])->post('https://extbot02.extensyaai.com/api/v1/bots/'.$botName.'/converse/'.$user, [
            'type' => 'text',
            'text' => $message,
        ]);

        $statusCode = $response->status();

        if ($statusCode != 200){
            // Mail::to(['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>'])
            //     ->send(new TestReply('ChatBot Have Errors',$response,[]));

            // $response ='{
            //                 "responses": [
            //                     {
            //                         "type": "text",
            //                         "workflow": {},
            //                         "text": "سيتم توصيلك بأحد وكلائنا قريبًا، شكرًا لك على سعة صدرك.",
            //                         "variations": [
            //                             "سيتم توصيلك بأحد وكلائنا قريبًا، شكرًا لك على سعة صدرك."
            //                         ]
            //                     }
            //                 ]
            //             }';

            // return $response;

        }else{
            return $response;
        }

    }

}
