<?php

namespace App\Exports\NewsLetter;

use App\Models\Tenant\NewsLetterEmail;
use Maatwebsite\Excel\Concerns\FromCollection;

use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMapping;

class ReportExport implements FromCollection, WithMapping, WithHeadings
{
    use Exportable;

    protected $reportPageGroup;
    protected $reportPageEmail;
    protected $reportPageFrom;
    protected $reportPageTo;

    public function __construct($reportPageGroup, $reportPageEmail, $reportPageFrom, $reportPageTo)
    {
        $this->reportPageGroup = $reportPageGroup;
        $this->reportPageEmail = $reportPageEmail;
        $this->reportPageFrom = $reportPageFrom;
        $this->reportPageTo = $reportPageTo;
    }



    public function collection()
    {
        return NewsLetterEmail::query()
            ->when($this->reportPageGroup != 'all', function ($query) {
                $query->where('news_letter_group_id', $this->reportPageGroup);
            })
            ->when($this->reportPageEmail != null, function ($query2) {
                $query2->where('email', 'like', '%'.$this->reportPageEmail.'%');
            })
            ->when($this->reportPageFrom != null, function ($query3) {
                $query3->where('updated_at', '>=', $this->reportPageFrom);
            })
            ->when($this->reportPageTo != null, function ($query4) {
                $query4->where('updated_at', '<=', $this->reportPageTo);
            })->get();
    }


    public function map($report): array
    {
        return [
            $report->email,
            $report->group->group_name,
            $report->status ? "Sent" : "Unsent" ,
            $report->error_message,
            $report->created_at,
            $report->updated_at,

        ];
    }

    public function headings(): array
    {
        return [
            'Email',
            'Group Name',
            'Status',
            'Error Message',
            'Created At',
            'Updated At',
        ];
    }
}
