<?php

namespace App\Exports\Reports\Users;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class DailyStaffedExport implements FromCollection, WithHeadings, WithMapping, ShouldAutoSize
{
    protected $dailyStaffedData;

    public function __construct($dailyStaffedData)
    {
        $this->dailyStaffedData = $dailyStaffedData;
    }

    public function collection()
    {
        return collect($this->dailyStaffedData);
    }

    public function map($row): array
    {
        $user = $row['user'] ?? null;
        $userName = '';
        
        if ($user) {
            $firstName = $user['first_name'] ?? '';
            $lastName = $user['last_name'] ?? '';
            $userName = trim($firstName . ' ' . $lastName);
        }
        
        return [
            $row['day'] ?? '',
            $user['username'] ?? '',
            $userName,
            $row['total_duration'] ?? '',
        ];
    }

    public function headings(): array
    {
        return [
            'Date',
            'User ID',
            'User Name',
            'Staffed Hours',
        ];
    }
}
