<?php

namespace App\Console\Commands\Youtube;

use Illuminate\Console\Command;
use App\Models\Tenant;
use Illuminate\Support\Facades\Http;

class GetData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'youtube:comments';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $tenants = Tenant::with('domains')->get();

            foreach ($tenants as $tenant)
            {
                foreach ($tenant->domains as $domain)
                {
                    Http::get('http://'.$domain->domain.'/webhook/youtube');
                }

            }
        }catch (\Exception $e)
        {
            //            Mail::raw($e->getMessage(), function ($message) {
            //                $message->to('<EMAIL>')->subject('Exception Google Get Reviews Token');
            //            });
        }
    }
}
