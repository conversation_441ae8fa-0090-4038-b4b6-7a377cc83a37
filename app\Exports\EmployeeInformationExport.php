<?php

namespace App\Exports;

use App\Models\Tenant\User;

use App\Models\Tenant\WfmBreak;
use App\Models\Tenant\WfmSkill;
use Illuminate\Support\Facades\Request;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;

class EmployeeInformationExport implements FromCollection, WithHeadings
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public $defaultAccount;


    public function collection()
    {
        $result= User::query()->with('wfmUserSkill' , 'role')->get();

        $url = Request::url();


        return $result->map(function ($item) {
  
            return [
                'Full Name' => $item->first_name,
                'User Name' => $item->username,
                'Account' => $this->defaultAccount,
                'Skill' => WfmSkill::find($item->wfmUserSkill?->wfm_skill_id)?->skill_name ?WfmSkill::find($item->wfmUserSkill?->wfm_skill_id)?->skill_name: '-',
                'Role' => $item->role[0]->name,
                'Status' => $item->status == '1' ? 'Active' : 'InActive',
             
            ];
       
        });
        
    }

    public function headings(): array
    {
            return [
                'Full Name',
                'User Name',
                'Account' ,
                'Skill' ,
                'Role' ,
                'Status'
        ];
      
      
    }
}
