<?php

namespace App\Console\Commands\System;

use App\Models\Tenant;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class SocialEscalation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'social-escalation:new';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $tenants = Tenant::with('domains')->get();

            foreach ($tenants as $tenant)
            {
                foreach ($tenant->domains as $domain)
                {
                    Http::get('https://'.$domain->domain.'/escalation/social-escalation');
                }
                //                Http::get('https://'.$tenant->domains[0]->domain.'/escalation');
            }
        }catch (\Exception $e)
        {
            //            Mail::raw($e->getMessage(), function ($message) {
            //                $message->to('<EMAIL>')->subject('Exception Agent logout | agent:logout');
            //            });
        }
    }
}
