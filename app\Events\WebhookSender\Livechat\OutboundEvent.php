<?php

namespace App\Events\WebhookSender\Livechat;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class OutboundEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $data;
    public $sender;

    public function __construct($sender, $data)
    {
        $this->sender = $sender;
        $this->data = $data;
    }
}
