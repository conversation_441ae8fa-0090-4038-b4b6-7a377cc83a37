<?php

namespace App\Exports;

use App\Models\Tenant\User;
use App\Models\Tenant\WfmBreak;

use Illuminate\Support\Facades\Request;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;


class AgentBreakExport implements FromCollection, WithHeadings
{
    /**
    * @return \Illuminate\Support\Collection
    */

public $defaultAccount;
    


    public function collection()
    {
        $result  =  WfmBreak::query()->with('User','schedules','skill')->get();
        $url = Request::url();
        $this->defaultAccount = explode('.', parse_url($url, PHP_URL_HOST))[0];


        return $result->map(function ($item) {
          
            return [
                'First Name' => $item->User->first_name,
                'Last Name' => $item->User->last_name,
                'User Name' => $item->User->username,
                'DATE' => $item->schedules->date,
                'BREAK FROM' => $item->break_from,
                'BREAK To' => $item->break_to,
                'Account' => $this->defaultAccount,
                'Skill Name' => $item->skill->skill_name,
            ];
       
        });
        
    }

    public function headings(): array
    {
            return [
            'First Name', 
            'Last Name',
            'Last Name', 
            'DATE' ,
            'BREAK FROM',
            'BREAK To' ,
            'Account' ,
            'Skill Name',
        ];
      
      
    }

}
