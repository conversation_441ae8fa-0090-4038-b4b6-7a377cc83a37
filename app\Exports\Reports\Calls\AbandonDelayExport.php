<?php

namespace App\Exports\Reports\Calls;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class AbandonDelayExport implements FromCollection, WithHeadings
{
    protected $records;
    protected $headings;

    public function __construct($records, $headings)
    {
        $this->records = $records;
        $this->headings = $headings;
    }

    public function collection()
{
    return $this->records->map(function ($record) {
        return [
            'queue_name' => $record['queue_name'],
            'interaction' => $record['interaction'],
            'day' => $record['day'],
            't1' => $record['t1'] === 0 ? '0' : $record['t1'],
            't2' => $record['t2'] === 0 ? '0' : $record['t2'],
            't3' => $record['t3'] === 0 ? '0' : $record['t3'],
            't4' => $record['t4'] === 0 ? '0' : $record['t4'],
            't5' => $record['t5'] === 0 ? '0' : $record['t5'],
            't6' => $record['t6'] === 0 ? '0' : $record['t6'],
            't7' => $record['t7'] === 0 ? '0' : $record['t7'],
            't8' => $record['t8'] === 0 ? '0' : $record['t8'],
            't9' => $record['t9'] === 0 ? '0' : $record['t9'],
            't10' => $record['t10'] === 0 ? '0' : $record['t10'],
            'p1' => $record['p1'] === 0 ? '0' : $record['p1'],
            'p2' => $record['p2'] === 0 ? '0' : $record['p2'],
            'p3' => $record['p3'] === 0 ? '0' : $record['p3'],
            'p4' => $record['p4'] === 0 ? '0' : $record['p4'],
            'p5' => $record['p5'] === 0 ? '0' : $record['p5'],
            'p6' => $record['p6'] === 0 ? '0' : $record['p6'],
            'p7' => $record['p7'] === 0 ? '0' : $record['p7'],
            'p8' => $record['p8'] === 0 ? '0' : $record['p8'],
            'p9' => $record['p9'] === 0 ? '0' : $record['p9'],
            'p10' => $record['p10'] === 0 ? '0' : $record['p10'],
        ];
    });
}


    public function headings(): array
    {
        return $this->headings;
    }
} 