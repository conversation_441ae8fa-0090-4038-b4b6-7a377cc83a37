<?php

namespace App\Http\Controllers\Tenant\Auth;

use Carbon\Carbon;
use App\Models\Tenant\User;
use Illuminate\Http\Request;
use App\Events\System\AgentOut;
use App\Models\Tenant\AgentSession;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use App\Providers\RouteServiceProvider;
use Illuminate\Support\Facades\Session;
use App\Models\Tenant\AgentConfigration;
use App\Models\Tenant\AgentStatusAction;
use App\Models\Tenant\AgentStatusActionLog;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Foundation\Auth\AuthenticatesUsers;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = RouteServiceProvider::HOME;


    // =====================================
    // Lock the user after 5 wrong attempts:

    // Maximum number of attempts to allow
    // protected $maxAttempts = 5;
    // Number of minutes to lock the user out for after exceeding max attempts
    // protected $decayMinutes = 15;

    // =====================================

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }

    public function redirectTo()
    {
        if (auth()->user()->roles()->first()->allowed_route != '') {

            return $this->redirectTo = '/' . auth()->user()->roles()->first()->allowed_route;
        }
    }


    public function login(Request $request)
    {
        $this->validateLogin($request);

        // code here for 5 wrong attempts ------------------------------------------------
        $user = User::where('username', $request->input('username'))->first();


        if ($user){
            if ($user->is_locked >= 5) {
                Auth::logout();
                Session::flash('error', 'Your account is locked, please contact the admin');
                return back();

            } else {

                if (Auth::attempt($request->only('username', 'password'))) {
                    // If successful, reset `is_locked` to zero
                    $user->update(['is_locked' => 0]);
                } else {
                    // If login fails, increment `is_locked` by one
                    if ($user) {
                        $user->increment('is_locked');
                    }
                }
            }
        }


        //********************************************************* */

        // If the class is using the ThrottlesLogins trait, we can automatically throttle
        // the login attempts for this application. We'll key this by the username and
        // the IP address of the client making these requests into this application.
        if (
            method_exists($this, 'hasTooManyLoginAttempts') &&
            $this->hasTooManyLoginAttempts($request)
        ) {
            $this->fireLockoutEvent($request);

            return $this->sendLockoutResponse($request);
        }

        if ($this->attemptLogin($request)) {
            if ($request->hasSession()) {
                $request->session()->put('auth.password_confirmed_at', time());
            }

            return $this->sendLoginResponse($request);
        }

        // If the login attempt was unsuccessful we will increment the number of attempts
        // to login and redirect the user back to the login form. Of course, when this
        // user surpasses their maximum number of attempts they will get locked out.
        $this->incrementLoginAttempts($request);

        return $this->sendFailedLoginResponse($request);
    }


    // protected function authenticated(Request $request, $user)
    // {


    //     $_token = Session::get('_token');



    //     $config = AgentConfigration::first();

    //     Session::put('session_time_out' , $config->session_time_out);
    //     Session::put('redirect_to_queue', $config->redirect_to_queue);
    //     Session::put('logout_status'    , $config->logout_status);

    //     $oldSession = AgentSession::where('user_id',auth()->id())->where('logout_at',null)->whereNull('logout_at')->orderBy('id','desc')->first();

    //     $AgentSessionId = AgentSession::create(
    //         [
    //             'login_at'  => Carbon::now(),
    //             'user_id'   => auth()->id(),
    //             'token'     => $_token,
    //         ]
    //     );


    //     //GET TICKET INFO

    //     $userCode = AgentStatusAction::where('user_id', auth()->id())->first();

    //     if ($userCode)
    //     {
    //         $userCode->update(['agent_status_id' => 1]);
    //     }else{
    //         AgentStatusAction::create(['agent_status_id' => 1,'user_id'=> auth()->id()]);
    //     }

    //     AgentStatusAction::where('user_id', auth()->id())->first()->update(['agent_status_id' => 4]);



    //     //INSERT AGENT STATUS ACTION LOG
    //     AgentStatusActionLog::create(['agent_session_id' => $AgentSessionId->id, 'agent_status_id' =>1 , 'user_id' => auth()->id()]);

    //     AgentStatusActionLog::create(['agent_session_id' => $AgentSessionId->id, 'agent_status_id' => 4, 'user_id' => auth()->id()]);


    //     if ($oldSession)
    //     {

    //         broadcast(new AgentOut(tenant('id'), auth()->id(),$_token));

    //         $old = AgentSession::where('user_id',auth()->id())->where('logout_at',null)->whereNull('logout_at')->where('token','!=',$_token)->get();

    //             foreach ($old as $o){
    //                 $o->update(['logout_at' => Carbon::now()]);
    //             }


    //     }




    //     if (Cache::has('role_routes')){Cache::forget('role_routes');}

    //     if (Cache::has('user_routes')){Cache::forget('role_routes');}
    // }

    protected function authenticated(Request $request, $user)
    {

        if ($user && $user->password_last_reset_at === null) {
            Auth::logout();
            Session::flash('password_reset_required', 'Change Password. You must reset your password on first login.');
            return redirect()->route('password.request');
        }
        if (Carbon::parse($user?->password_last_reset_at)->lt(now()->subMonths(1))) {

            Auth::logout();
            Session::flash('password_reset_required', 'Password Expired, You must reset your password every month.');
            return redirect()->route('password.request');
        }


        if (!auth()->check()) {
            return redirect()->route('login')->withErrors(['login' => 'User is not authenticated.']);
        }

        $_token = Session::get('_token');

        $config = AgentConfigration::first();

        Session::put('session_time_out', $config->session_time_out);
        Session::put('redirect_to_queue', $config->redirect_to_queue);
        Session::put('logout_status', $config->logout_status);

        $oldSession = AgentSession::where('user_id', auth()->id())
            ->where('logout_at', null)
            ->whereNull('logout_at')
            ->orderBy('id', 'desc')
            ->first();

        $AgentSessionId = AgentSession::create(
            [
                'login_at' => Carbon::now(),
                'user_id' => auth()->id(),
                'token' => $_token,
            ]
        );

        // GET TICKET INFO
        $userCode = AgentStatusAction::where('user_id', auth()->id())->first();

        if ($userCode) {
            $userCode->update(['agent_status_id' => 1]);
        } else {
            AgentStatusAction::create(['agent_status_id' => 1, 'user_id' => auth()->id()]);
        }

        AgentStatusAction::where('user_id', auth()->id())->first()->update(['agent_status_id' => 4]);

        // INSERT AGENT STATUS ACTION LOG
        AgentStatusActionLog::create([
            'agent_session_id' => $AgentSessionId->id,
            'agent_status_id' => 1,
            'user_id' => auth()->id()
        ]);

        AgentStatusActionLog::create([
            'agent_session_id' => $AgentSessionId->id,
            'agent_status_id' => 4,
            'user_id' => auth()->id()
        ]);

        if ($oldSession) {
            broadcast(new AgentOut(tenant('id'), auth()->id(), $_token));

            $old = AgentSession::where('user_id', auth()->id())
                ->where('logout_at', null)
                ->whereNull('logout_at')
                ->where('token', '!=', $_token)
                ->get();

            foreach ($old as $o) {
                $o->update(['logout_at' => Carbon::now()]);
            }
        }

        if (Cache::has('role_routes')) {
            Cache::forget('role_routes');
        }

        if (Cache::has('user_routes')) {
            Cache::forget('role_routes');
        }
    }



    protected function loggedOut(Request $request)
    {

        Cache::forget('role_routes');
        Cache::forget('user_routes');
    }

    public function showLoginForm()
    {

        return view('tenant.auth.login');
    }

    public function username()
    {
        return 'username';
    }
}
