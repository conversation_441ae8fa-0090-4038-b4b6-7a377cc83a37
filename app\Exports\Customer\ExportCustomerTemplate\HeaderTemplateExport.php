<?php

namespace App\Exports\Customer\ExportCustomerTemplate;

use App\Models\Tenant\ContactProfileGroup;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class HeaderTemplateExport implements FromCollection, WithHeadings, WithMapping
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        // You can return an empty collection or just create a dummy row for the template
        return collect([new \stdClass()]);
    }

    /**
     * Map the data to include roles and departments
     *
     * @param  mixed  $user
     * @return array
     */
    public function map($user): array
    {
        $groups = ContactProfileGroup::query()
            ->pluck('name')
            ->implode(',');
        return [
            '',
            '',
            '',
            '',
            $groups,
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
        ];
    }

    /**
     * Define the headers for the export
     *
     * @return array
     */
    public function headings(): array
    {
        return [
            'Name',
            'Primary Email',
            'Country Code Number',
            'Primary Phone Number',
            'Group Type',
            'Email',
            'Phone',
            'X',
            'Instagram',
            'Facebook',
            'LinkedIn',
            'YouTube',
            'WhatsApp',
            'Snapchat',
            'TikTok',
            'Google',
        ];
    }
}
