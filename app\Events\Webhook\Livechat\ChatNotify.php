<?php

namespace App\Events\Webhook\Livechat;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ChatNotify implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $tenant_id;
    public $message;

    public function __construct($tenant_id, $message)
    {
        $this->tenant_id    =   $tenant_id;
        $this->message      =   $message;
    }


    public function broadcastWith()
    {
        return [
            'tenant_id'     =>$this->tenant_id,
            'content'       =>$this->message,

        ];
    }

    public function broadcastOn()
    {
        return new PrivateChannel('Livechat.'.$this->tenant_id);
    }
}
