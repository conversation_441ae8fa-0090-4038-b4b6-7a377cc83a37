<?php

namespace App\Helper;

use App\Models\Tenant\SystemLogs;
use Illuminate\Support\Facades\Auth;

class LogHelper
{
    /**
     * Log an action performed on a model.
     *
     * @param string $action
     * @param string $model
     * @param int|null $recordId
     * @param string $pageLink
     * @param string $description
     * @param array|null $oldData
     * @param array|null $newData
     * @return void
     */
    public static function logAction($model, $recordId = null, $action = '',$description = '', $oldData = null, $newData = null)
    {
        $pageLink = request()->is('livewire/*') 
                    ? strtok(url()->previous(), '?')
                    : strtok(url()->current(), '?'); 

        SystemLogs::create([
            'user_id' => Auth::id(),
            'model' => $model,
            'record_id' => $recordId,
            'page_link' => $pageLink,
            'action' => $action,
            'description' => $description,
            'old_data' => $oldData ? json_encode($oldData) : null,
            'new_data' => $newData ? json_encode($newData) : null,
        ]);
    }
}
