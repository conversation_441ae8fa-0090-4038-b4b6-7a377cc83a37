<?php

namespace App\Exports\Reports\Rating;

use App\Models\Tenant\GoogleReviewLocation;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class GoogleExport  implements FromCollection, WithHeadings, WithMapping, ShouldAutoSize
{
    protected $records;

    public function __construct($records)
    {
        $this->records = $records;
    }

    public function collection()
    {
        // Transform the key-value pairs into objects for mapping
        $transformedData = [];

        foreach ($this->records as $locationId => $rating) {
            $transformedData[] = (object) [
                'location_id' => $locationId,
                'rating' => $rating
            ];
        }

        return collect($transformedData);
    }

    public function map($row): array
    {
        // Get location name from the location_id
        $locationName = GoogleReviewLocation::where('location_id', $row->location_id)
            ->first()
            ->location_name ?? 'Unknown Location';

        $data = [
            $locationName,
            number_format($row->rating, 2) . '/5',
        ];

        return $data;
    }

    public function headings(): array
    {
        $headings = [
            'Location',
            'Rate',
        ];
        return $headings;
    }
}
