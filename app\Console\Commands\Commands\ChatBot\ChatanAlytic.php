<?php

namespace App\Console\Commands\Commands\ChatBot;

use Illuminate\Console\Command;
use App\Models\Tenant;
use Illuminate\Support\Facades\Http;

class ChatanAlytic extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:chatan-alytic';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $tenants = Tenant::with('domains')->get();

            foreach ($tenants as $tenant)
            {
                foreach ($tenant->domains as $domain)
                {

                   Http::get('http://'.$domain->domain.'/chatbot-statistics-job');
                }

            }

        }catch (\Exception $e)
        {
            dd($e);

        }
    
    }
}
