<?php

namespace App\Exports\Reports\Users;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class StatusExport implements FromCollection, WithHeadings, WithMapping, ShouldAutoSize
{
    protected $statusData;
    protected $auxTypes;

    public function __construct($statusData, $auxTypes = [])
    {
        $this->statusData = $statusData;
        $this->auxTypes = $auxTypes;
    }

    public function collection()
    {
        return collect($this->statusData);
    }

    public function map($row): array
    {
        $data = [
            $row['user_id'] ?? '',
            $row['name'] ?? '',
            $row['date'] ?? '',
            $row['total_staff_time'] ?? '',
        ];
        foreach ($this->auxTypes as $auxType) {
            $data[] = $row['AUX'][$auxType] ?? '00:00:00';
        }
        return $data;
    }

    public function headings(): array
    {
        $headings = [
            'User ID',
            'Name',
            'Date',
            'Total Staff Time',
        ];
        foreach ($this->auxTypes as $auxType) {
            $headings[] = $auxType;
        }
        return $headings;
    }
}
