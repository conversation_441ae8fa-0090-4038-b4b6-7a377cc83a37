<?php

namespace App\Events\Ticket;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class TicketReassign implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $tenant_id;
    public $department_id;
    public $channel;
    public $content;
    public $icon;

    /**
     * Create a new event instance.
     */
    public function __construct($tenant_id,$department_id,$channel,$content,$icon)
    {
       
        $this->tenant_id        =   $tenant_id;
        $this->department_id    =   $department_id;
        $this->channel          =   $channel;
        $this->content          =   $content;
        $this->icon             =   $icon;

    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastWith()
    {
        return [
            'tenant_id'         =>$this->tenant_id,
            'department_id'     =>$this->department_id,
            'channel'           =>$this->channel,
            'content'           =>$this->content,
            'icon'              =>$this->icon
        ];
    }


    public function broadcastOn()
    {
        return new PrivateChannel('TicketReassign.'.$this->tenant_id);
    }
}
