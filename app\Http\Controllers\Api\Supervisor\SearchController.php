<?php

namespace App\Http\Controllers\Api\Supervisor;

use App\Http\Controllers\Controller;
use App\Http\Resources\Supervisor\Search\ResourceResource;
use App\Http\Resources\Supervisor\Search\TicketResource;
use App\Models\Tenant\Resource;
use App\Models\Tenant\Ticket;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class SearchController extends Controller
{
    public function tickets(Request $request)
    {
        $customerName = isset($request->customerName) && $request->customerName != '' ? $request->customerName : null;
        $ticketId = isset($request->ticketId) && $request->ticketId != '' ? $request->ticketId : null;
        $resourceId = isset($request->resourceId) && $request->resourceId != '' ? $request->resourceId : null;
        $agentId = isset($request->agentId) && $request->agentId != '' ? $request->agentId : null;
        $departmentId = isset($request->departmentId) && $request->departmentId != '' ? $request->departmentId : null;
        $fromDate = isset($request->fromDate) && $request->fromDate != '' ? $request->fromDate : null;
        $toDate = isset($request->toDate) && $request->toDate != '' ? $request->toDate : null;

        $limit = isset($request->limit) && $request->limit != '' ? $request->limit : 100;


        $tickets = Ticket::with(['ticketInfo', 'ticketActions' => function ($q) {
            $q->with(['fields', 'categories']);
        }]);

        if ($ticketId != null) {
            $tickets->where('id', $ticketId);
        }

        if ($resourceId != null) {
            $tickets->where('resource_id', $resourceId);
        }

        if ($agentId != null) {
            $tickets->where('user_id', $agentId);
        }

        if ($departmentId != null) {
            $tickets->where('department_id', $departmentId);
        }

        if ($customerName != null) {
            $tickets->whereHas('ticketCustomer', function ($query) use ($customerName) {
                $query->where('display_name', 'like', '%' . $customerName . '%');
            });
        }

        if ($fromDate != null) {
            $fromDate = date($fromDate . ' 00:00:00');
            $tickets->where('created_at', '>=', $fromDate);
        }


        if ($toDate != null) {
            $toDate = date($toDate . ' 23:59:59');
            $tickets->where('created_at', '<=', $toDate);
        }

        $tickets = $tickets->orderBy('id', 'desc')->limit($limit)->get();


        if ($tickets->count() > 0) {
            return TicketResource::collection($tickets);
        } else {
            return response()->json(['error' => true, 'message' => 'No tickets found'], 200);
        }

    }

    public function resources(Request $request)
    {
        $customer = isset($request->customer) && $request->customer != '' ? $request->customer : null;
        $resourceId = isset($request->resourceId) && $request->resourceId != '' ? $request->resourceId : null;
        $agentId = isset($request->agentId) && $request->agentId != '' ? $request->agentId : null;
        $channel = isset($request->channel) && $request->channel != '' ? $request->channel : null;
        $fromDate = isset($request->fromDate) && $request->fromDate != '' ? $request->fromDate : null;
        $toDate = isset($request->toDate) && $request->toDate != '' ? $request->toDate : null;

        $limit = isset($request->limit) && $request->limit != '' ? $request->limit : 100;

        $resources = Resource::with(['resourceInfo', 'resourceActions', 'resourceCategories', 'resourceFields', 'resourceNote', 'resourceGroup']);

        if ($resourceId != null) {
            $resources->where('id', $resourceId);
        }

        if ($agentId != null) {
            $resources->where('user_id', $agentId);
        }

        if ($customer != null) {
            $resources->whereHas('resourceInfo.customer', function ($query) use ($customer) {
                $query->where('display_name', 'like', '%' . $customer . '%');
            });
        }


        if ($channel != null) {
            $channel = Str::ucfirst(Str::lower($channel));
            $resources->whereHas('resourceInfo', function ($query) use ($channel) {
                $query->where('channel', 'like', '%' . $channel . '%');
            });
        }

        if ($fromDate != null) {
            $fromDate = date($fromDate . ' 00:00:00');
            $resources->where('created_at', '>=', $fromDate);
        }

        if ($toDate != null) {
            $toDate = date($toDate . ' 23:59:59');
            $resources->where('created_at', '<=', $toDate);
        }

        $resources = $resources->orderBy('id', 'desc')->limit($limit)->get();

        if ($resources->count() > 0) {
            return ResourceResource::collection($resources);
        } else {
            return response()->json(['error' => true, 'message' => 'No resource found'], 200);
        }

    }


    public function agentTickets(Request $request)
    {
        $customerName = isset($request->customerName) && $request->customerName != '' ? $request->customerName : null;
        $ticketId = isset($request->ticketId) && $request->ticketId != '' ? $request->ticketId : null;
        $resourceId = isset($request->resourceId) && $request->resourceId != '' ? $request->resourceId : null;
        $agentId = isset($request->agentId) && $request->agentId != '' ? $request->agentId : null;
        $departmentId = isset($request->departmentId) && $request->departmentId != '' ? $request->departmentId : null;
        $fromDate = isset($request->fromDate) && $request->fromDate != '' ? $request->fromDate : null;
        $toDate = isset($request->toDate) && $request->toDate != '' ? $request->toDate : null;

        $limit = isset($request->limit) && $request->limit != '' ? $request->limit : 100;


        $tickets = Ticket::with(['ticketInfo', 'ticketActions' => function ($q) {
            $q->with(['fields', 'categories']);
        }])->where('user_id',auth()->id());

        if ($ticketId != null) {
            $tickets->where('id', $ticketId);
        }

        if ($resourceId != null) {
            $tickets->where('resource_id', $resourceId);
        }

        if ($agentId != null) {
            $tickets->where('user_id', $agentId);
        }

        if ($departmentId != null) {
            $tickets->where('department_id', $departmentId);
        }

        if ($customerName != null) {
            $tickets->whereHas('ticketCustomer', function ($query) use ($customerName) {
                $query->where('display_name', 'like', '%' . $customerName . '%');
            });
        }

        if ($fromDate != null) {
            $fromDate = date($fromDate . ' 00:00:00');
            $tickets->where('created_at', '>=', $fromDate);
        }


        if ($toDate != null) {
            $toDate = date($toDate . ' 23:59:59');
            $tickets->where('created_at', '<=', $toDate);
        }

        $tickets = $tickets->orderBy('id', 'desc')->limit($limit)->get();


        if ($tickets->count() > 0) {
            return TicketResource::collection($tickets);
        } else {
            return response()->json(['error' => true, 'message' => 'No tickets found'], 200);
        }

    }

    public function agentResources(Request $request)
    {
        $customer = isset($request->customer) && $request->customer != '' ? $request->customer : null;
        $resourceId = isset($request->resourceId) && $request->resourceId != '' ? $request->resourceId : null;
        $agentId = isset($request->agentId) && $request->agentId != '' ? $request->agentId : null;
        $channel = isset($request->channel) && $request->channel != '' ? $request->channel : null;
        $fromDate = isset($request->fromDate) && $request->fromDate != '' ? $request->fromDate : null;
        $toDate = isset($request->toDate) && $request->toDate != '' ? $request->toDate : null;

        $limit = isset($request->limit) && $request->limit != '' ? $request->limit : 100;

        $resources = Resource::with(['resourceInfo', 'resourceActions', 'resourceCategories', 'resourceFields', 'resourceNote', 'resourceGroup'])->where('user_id',auth()->id());

        if ($resourceId != null) {
            $resources->where('id', $resourceId);
        }

        if ($agentId != null) {
            $resources->where('user_id', $agentId);
        }

        if ($customer != null) {
            $resources->whereHas('resourceInfo.customer', function ($query) use ($customer) {
                $query->where('display_name', 'like', '%' . $customer . '%');
            });
        }


        if ($channel != null) {
            $channel = Str::ucfirst(Str::lower($channel));
            $resources->whereHas('resourceInfo', function ($query) use ($channel) {
                $query->where('channel', 'like', '%' . $channel . '%');
            });
        }

        if ($fromDate != null) {
            $fromDate = date($fromDate . ' 00:00:00');
            $resources->where('created_at', '>=', $fromDate);
        }

        if ($toDate != null) {
            $toDate = date($toDate . ' 23:59:59');
            $resources->where('created_at', '<=', $toDate);
        }

        $resources = $resources->orderBy('id', 'desc')->limit($limit)->get();

        if ($resources->count() > 0) {
            return ResourceResource::collection($resources);
        } else {
            return response()->json(['error' => true, 'message' => 'No resource found'], 200);
        }

    }
}
