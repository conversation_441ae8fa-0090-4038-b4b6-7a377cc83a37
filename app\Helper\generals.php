<?php

function timer($seconds)
{
    $hours = floor($seconds / 3600);
    $mins = floor($seconds / 60 % 60);
    $secs = floor($seconds % 60);

    $timeFormat = sprintf('%02d:%02d:%02d', $hours, $mins, $secs);

    return $timeFormat;

}


if (!function_exists('ordinal')) {
    function ordinal($number) {
        $suffix = 'th';
        if (!in_array(($number % 100), [11, 12, 13])) {
            switch ($number % 10) {
                case 1:  $suffix = 'st'; break;
                case 2:  $suffix = 'nd'; break;
                case 3:  $suffix = 'rd'; break;
            }
        }
        return $number . $suffix;
    }
}

