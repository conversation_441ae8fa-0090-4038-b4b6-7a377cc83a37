<?php

namespace App\Exports\Ticket;

use App\Models\Tenant\Ticket;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Illuminate\Contracts\Queue\ShouldQueue;
use Maatwebsite\Excel\Concerns\Exportable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Bus\Queueable;
use Maatwebsite\Excel\Concerns\WithMapping;
use Illuminate\Support\Collection;

class GeneralDataReportExport implements FromCollection, WithHeadings, WithMapping
{
    protected $from;
    protected $to;

    public function __construct($from, $to)
    {
        $this->from = $from;
        $this->to = $to;
    }

    // Retrieve the data collection
    public function collection()
    {
        return Ticket::query()
            ->whereDate('created_at', '>=', $this->from)
            ->whereDate('created_at', '<=', $this->to)
            ->with(['department', 'ticketActions.user', 'ticketCustomer', 'ticketInfo'])
            ->get();
    }

    // Map the data to match the table's structure
    public function map($ticket): array
    {
        // Get the latest ticket action
        $latestTicketAction = $ticket->ticketActions()->latest()->first();

        if($ticket->status == 'Closed'){
            $departmentName = $ticket->ticketActions()->where('department_id', '!=',0)->orderBy('id','desc')->first()->department->name ?? '';
        }


        else{
            $departmentName = $ticket->department->name ?? '';
        }



        return [
            $ticket->id,
            $ticket->status,
            $departmentName,
            $latestTicketAction ? optional($latestTicketAction->user)->username ?? '' : '',
            $latestTicketAction ? optional($latestTicketAction->user)->first_name . ' ' . optional($latestTicketAction->user)->last_name : '',
            $ticket->ticketCustomer->display_name ?? '',
            $ticket->created_at->format('Y/m/d H:i:s'),
            $ticket->ticketInfo->channel ?? '',
            $latestTicketAction->comment ?? '',
        ];
    }

    // Define the headings for the table
    public function headings(): array
    {
        return [
            'Ticket ID',
            'Status',
            'Department',
            'User OPS',
            'User Name',
            'Customer Name',
            'Date and Time',
            'Source',
            'Comment',
        ];
    }
}
