<?php

namespace App\Http\Controllers\Api\Agent\Action;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Resource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class NonVoiceController extends Controller
{
    public function resolve(Request $request)
    {
        $validation = Validator::make($request->all(), [
            'resourceId' => 'required',
            'comment' => 'required',
        ]);

        if ($validation->fails()){
            return response()->json(['errors' => true, 'message' => $validation->errors()], 200);
        }

        $resource = Resource::whereId($request->resourceId)->first();

        if (!$resource)
        {
            return response()->json(['errors' => true, 'message' => 'No Resource found'], 200);
        }


            $ticket['status']           = 'Resolved';
            $ticket['user_id']          = $resource->user_id;


            $ticket = $resource;

            $ticketAction['action']         = 'Resolved';

            $ticket->update([
                'status' => 'Resolved',
            ]);

            $ticket->resourceInfo->update(['content'=>$request->comment]);



            $ticketAction['user_id']        = auth()->id();
            $ticketAction['comment']        = $request->comment;

            $ticket->resourceActions()->create($ticketAction);





        return response()->json(['message' => 'successfully resolved'], 200);

    }

    public function close(Request $request)
    {
        $validation = Validator::make($request->all(), [
            'resourceId' => 'required',
            'comment' => 'required',
        ]);

        if ($validation->fails()){
            return response()->json(['errors' => true, 'message' => $validation->errors()], 200);
        }

        $resource = Resource::whereId($request->resourceId)->first();

        if (!$resource)
        {
            return response()->json(['errors' => true, 'message' => 'No Resource found'], 200);
        }


        $ticket['status']           = 'Closed';
        $ticket['user_id']          = $resource->user_id;


        $ticket = $resource;

        $ticketAction['action']         = 'Closed';

        $ticket->update([
            'status' => 'Closed',
        ]);

        $ticket->resourceInfo->update(['content'=>$request->comment]);



        $ticketAction['user_id']        = auth()->id();
        $ticketAction['comment']        = $request->comment;

        $ticket->resourceActions()->create($ticketAction);





        return response()->json(['message' => 'successfully closed'], 200);
    }
}
