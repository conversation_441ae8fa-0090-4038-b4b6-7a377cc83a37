<?php

namespace App\Http\Controllers\Api\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class IndexController extends Controller
{

    public function login(Request $request)
    {
        
        $url = $request->url();
        
        $parsedUrl = parse_url($url);
        $host = $parsedUrl['host'];

        $parts = explode('.', $host);
        $subdomain = $parts[0];

        if (Auth::attempt(['username' => $request->username, 'password' => $request->password])) {
            $user = Auth::user();
            $email = $user->email;
            // dd($host,$email, $request->password);
            return $this->getRefreshedToken($host,$email, $request->password);
        } else {
            
            return response()->json(['error' => true, 'message' => 'Unauthorized'],401);
        }
    }

    public function getRefreshedToken($host,$email, $password)
    {

        $verifyValue = app()->environment() == 'local' ? false : true;

     
        $response = Http::withOptions([
            'verify' => $verifyValue,
        ])->post('https://'.$host.'/oauth/token', [

            'grant_type' => 'password',
            'client_id' => DB::table('oauth_clients')->first()->id,
            'client_secret' => DB::table('oauth_clients')->first()->secret,
            'username' => $email,
            'password' => $password,
            'scope' => '*',
        ]);
 

        return response()->json($response->json(), 200);
    }

    public function refresh_token(Request $request)
    {
        try {

            $url = $request->url();

            $parsedUrl = parse_url($url);
            $host = $parsedUrl['host'];

            $parts = explode('.', $host);
            $subdomain = $parts[0];

            $refresh_token = $request->header('RefreshTokenCode');

            $verifyValue = app()->environment() == 'local' ? false : true;

            $response = Http::withOptions([
                'verify' => $verifyValue,
            ])->post('https://'.$host.'/oauth/token', [

                'grant_type' => 'refresh_token',
                'refresh_token' => $refresh_token,
                'client_id' => DB::table('oauth_clients')->first()->id,
                'client_secret' => DB::table('oauth_clients')->first()->secret,
                'scope' => '*',
            ]);

            return response()->json($response->json(), 200);
        } catch (\Exception $e) {
            return response()->json(['error' => true, 'message' => 'Unauthorized'],401);
        }

    }

    public function password_reset(Request $request)
    {
        $validation = Validator::make($request->all(),
            ['email' => 'required|email']
        );

        if ($validation->fails()){
            return response()->json(['errors' => true, 'message' => $validation->errors()], 200);
        }

        $response = $this->broker()->sendResetLink($this->credentials($request));

        return $response == Password::RESET_LINK_SENT
            ? $this->sendResetLinkResponse($request, $response)
            : $this->sendResetLinkFailedResponse($request, $response);
    }

    protected function credentials(Request $request)
    {
        return $request->only('email');
    }


    protected function sendResetLinkResponse(Request $request, $response)
    {
        return $request->wantsJson()
            ? new JsonResponse(['message' => trans($response)], 200)
            : back()->with('status', trans($response));
    }


    protected function sendResetLinkFailedResponse(Request $request, $response)
    {
        if ($request->wantsJson()) {
            throw ValidationException::withMessages([
                'email' => [trans($response)],
            ]);
        }

        return back()
            ->withInput($request->only('email'))
            ->withErrors(['email' => trans($response)]);
    }


    public function broker()
    {
        return Password::broker();
    }

}
