<?php

namespace App\Exports\Current;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class UserReportExport implements FromArray, WithHeadings
{
    protected $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * Return the data to be exported.
     */
    public function array(): array
    {
        return $this->data;
    }

    /**
     * Define the headings for the export.
     */
    public function headings(): array
    {
        return [
            'Full Name',
            'Role',
            'Email',
            'Username',
            'Status',
            'Active Days',
        ];
    }
}
