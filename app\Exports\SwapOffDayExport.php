<?php

namespace App\Exports;

use App\Models\Tenant\WfmSwap;
use App\Models\Tenant\WfmBreak;
use Illuminate\Support\Facades\Request;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;

class SwapOffDayExport implements FromCollection, WithHeadings
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public $defaultAccount;

    public function collection()
    {
        $result = WfmSwap::query()->with('userFrom','userTo','userSkill' ,'shiftFrom' ,'shiftTo')->where('type' , 'OFF')->get();
        $url = Request::url();
        $this->defaultAccount = explode('.', parse_url($url, PHP_URL_HOST))[0];



        return $result->map(function ($item) {
      
            return [
                'Request From' =>''.$item->userFrom->first_name.' '.$item->userFrom->last_name.'',
                'Request To' => ''.$item->userTo->first_name.' '.$item->userTo->last_name.'' ,
                'Shift From' =>$item->shiftFrom->title,
                'Shift To' =>$item->shiftTo->title,
                'Request Date From' =>$item->request_date_from,
                'Request Date To' =>$item->request_date_to,
                'Type' =>$item->type,
                'Status' =>$item->status,
                'User Skill' =>$item->userSkill->wfmSkill->skill_name,
                'Account' =>$this->defaultAccount,
                'Filled By' =>$item->filled_by,
                'Comment' =>$item->comment,
                'Expired At' =>$item->expired_at,
     
         
            ];
       
        });
        
    }

    public function headings(): array
    {
            return [
                'Request From',
                'Request To' ,
                'Shift From',
                'Shift To',
                'Request Date From',
                'Request Date To',
                'Type',
                'Status',
                'User Skill',
                'Account',
                'Filled By',
                'Comment',
                'Expired At',
        ];
    }
}
