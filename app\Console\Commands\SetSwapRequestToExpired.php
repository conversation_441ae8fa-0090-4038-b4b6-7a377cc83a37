<?php

namespace App\Console\Commands;

use App\Models\Tenant;
use App\Models\Tenant\WfmSwap;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class SetSwapRequestToExpired extends Command
{
    protected $signature = 'swap:expired';
    protected $description = 'Sets the pending swap request status to expired after passing the date';

    public function handle()
    {
        $this->info('Checking Expired Swaps');

        try {

            $tenants = Tenant::with('domains')->get();

            foreach ($tenants as $tenant) {
                foreach ($tenant->domains as $domain)
                {
//                    Http::get('https://'.$domain->domain.'/voice/calls');
                    Http::get('https://' . $domain->domain . '/webhook/agent/swap-expired');
                }

            }
        } catch (\Exception $e) {
            echo $e;
        }
    }
}
