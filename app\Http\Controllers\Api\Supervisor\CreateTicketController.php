<?php

namespace App\Http\Controllers\Api\Supervisor;

use App\Events\Ticket\SystemNotify;
use App\Helper\Mailer;
use App\Helper\Sms;
use App\Http\Controllers\Controller;
use App\Http\Resources\Supervisor\CreateTicket\ListCategoryLevelsResource;
use App\Http\Resources\Supervisor\CreateTicket\ListCategoryResource;
use App\Http\Resources\Supervisor\CreateTicket\ListCountryResource;
use App\Http\Resources\Supervisor\CreateTicket\ListFormFieldsResource;
use App\Http\Resources\Supervisor\CreateTicket\ListLangResource;
use App\Http\Resources\Supervisor\CreateTicket\ListPriorityResource;
use App\Mail\Ticket\NewTicketActionNotify;
use App\Mail\Ticket\NewTicketNotify;
use App\Models\Tenant\AgentQueue;
use App\Models\Tenant\Category;
use App\Models\Tenant\CategoryLevel;
use App\Models\Tenant\Department;
use App\Models\Tenant\Distribution;
use App\Models\Tenant\EmailScript;
use App\Models\Tenant\Escalation;
use App\Models\Tenant\FormField;
use App\Models\Tenant\Resource;
use App\Models\Tenant\SmsScript;
use App\Models\Tenant\Ticket;
use App\Models\Tenant\TicketFile;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class CreateTicketController extends Controller
{

    public function listPriority()
    {
        return ListPriorityResource::collection(['High','Medium','Low']);
    }

    public function listLang()
    {
        return ListLangResource::collection(['Arabic','English']);
    }

    public function listCountry()
    {

        $countries = new \PragmaRX\Countries\Package\Countries();

        $all = $countries->all()->pluck('name')->toArray();

        return ListCountryResource::collection($all);

    }

    public function listCategoryLevels()
    {
        $categoryLevels = CategoryLevel::whereStatus(1)->get();

        if ($categoryLevels->count() > 0) {
            return ListCategoryLevelsResource::collection($categoryLevels);
        } else {
            return response()->json(['error' => true, 'message' => 'No category level found'], 200);
        }
    }

    public function listCategory(Request $request)
    {

        $validation = Validator::make($request->all(), [
            'categoryLevel' => 'required',
            'parentId'        => 'nullable',
        ]);

        if ($validation->fails()){
            return response()->json(['errors' => true, 'message' => $validation->errors()], 200);
        }

        $categoryLevels = CategoryLevel::where('level',$request->categoryLevel)->first();


        if ($categoryLevels)
        {
            if (isset($request->parentId))
            {
                $parent = $request->parentId;
            }else{
                $parent = null;
            }

            $categories = Category::with(['distribution'])->whereStatus(1)->where('category_level_id',$categoryLevels->id)->where('parent_id',$parent)->get();

            if ($categories->count() > 0) {
                return ListCategoryResource::collection($categories);
            } else {
                return response()->json(['error' => true, 'message' => 'No category found'], 200);
            }
        }else{
            return response()->json(['error' => true, 'message' => 'No category level found'], 200);
        }

    }

    public function formFields(Request $request)
    {

        $validation = Validator::make($request->all(), [
            'formFieldsId' => 'required',
        ]);

        if ($validation->fails()){
            return response()->json(['errors' => true, 'message' => $validation->errors()], 200);
        }

        $distribution = Distribution::where('id',$request->formFieldsId)->first();


        if ($distribution)
        {
            $formData = isset($distribution->group->fields) ? $distribution->group->fields : ['error' => true, 'message' => 'No distribution found'];

            return ListFormFieldsResource::collection($formData);
        }else{
            return response()->json(['error' => true, 'message' => 'No form fields Id found'], 200);
        }

    }

    public function store(Request $request)
    {



        $validation = Validator::make($request->all(), [
            'type'          => 'required|in:voice,non-voice,bot,api',
            'typeFrom'      => 'required',
            'customerEmail' => 'nullable|email',
            'customerName'  => 'nullable',
            'customerNumber'=> 'nullable',
            'resourceId'    => Rule::requiredIf($request->type === 'non-voice'),
            'formFieldsId'  => 'required',
            'priority'      => 'nullable|in:High,Medium,Low,Critical',
            'language'      => 'nullable|in:English,Arabic',
//            'categoryIds' => 'required|array',
            'formFields' => 'required|json',
            'comment' => 'required',
        ]);



        if ($validation->fails()){
            return response()->json(['errors' => true, 'message' => $validation->errors()], 200);
        }

        if (isset($request->resourceId))
        {
            $resource = Resource::whereStatus('New')->find($request->resourceId);

            if (!$resource)
            {
                return response()->json(['errors' => true, 'message' => 'No resource found'], 200);
            }

            $distribution = Distribution::where('id',$request->formFieldsId)->first();

            if (!$distribution)
            {
                return response()->json(['errors' => true, 'message' => 'No form fields id found'], 200);
            }

            $countLevels        = $distribution->category->categoryLevel->level;

            if ($request->type == 'non-voice')
            {
                /*******Ticket*********/
                $ticket['status']           = 'New';
                $ticket['department_id']    = $distribution->department_id;
                $ticket['resource_id']      = $distribution->resource_id;

                $ticketQuery = Ticket::create($ticket);
                /*******Ticket*********/

                /*******TicketInfo*********/
                $ticketInfo['channel']      = $resource->resourceInfo->channel;
                $ticketInfo['type']         = $resource->resourceInfo->type;
                $ticketInfo['priority']     = $request->priority;
                $ticketInfo['language']     = $request->language;
                $ticketInfo['country']      = $request->country;
                $countries = new \PragmaRX\Countries\Package\Countries();
                $flag = $countries->where('name.common', $request->country)->first();
                if ($flag) {
                    $flagIcon = isset($flag->flag['flag-icon']) ? $flag->flag['flag-icon'] : false;
                    if ($flagIcon) {
                        $ticketInfo['countryFlag'] =$flagIcon;
                    } else {
                        $ticketInfo['countryFlag'] ='';
                    }
                } else {
                    $ticketInfo['countryFlag'] ='';
                }
                $ticketInfo['agent_id']     = auth('api')->id();
                $ticketInfo['comment']      = $request->comment;

                $ticketInfoQuery = $ticketQuery->ticketInfo()->create($ticketInfo);
                /*******TicketInfo*********/

                /*******TicketCustomer*********/
                $ticketCustomer['username']         = 'Unknown';
                $ticketCustomer['display_name']      = 'Unknown';
                $ticketCustomer['mobile_number']      = "Unknown";

                if (isset($request->customerEmail)){$ticketCustomer['username']         = $request->customerEmail;}
                if (isset($request->customerName)){$ticketCustomer['display_name']      = $request->customerName;}
                if (isset($request->customerNumber)){$ticketCustomer['mobile_number']   = $request->customerNumber;}

                $ticketCustomerQuery = $ticketQuery->ticketCustomer()->create($ticketCustomer);
                /*******TicketInfo*********/


                /*******TicketAction*********/
                $ticketAction['action']         = 'New Ticket';
                $ticketAction['user_id']        = auth('api')->id();
                $ticketAction['department_id']  = $distribution->department_id;
                $ticketAction['comment']        = $request->comment;
                $ticketActionQuery = $ticketQuery->ticketActions()->create($ticketAction);
                /*******TicketCustomer*********/

                /*******TicketFields*********/
                $ticketFields['fields'] = $request->formFields;
                $formFields = $request->formFields;

                $resultString=json_decode($formFields);

                $newFileds = [];
                foreach ($resultString as $key =>$value)
                {
                    $newFileds[Str::slug($key)] = $value;
                }
                $ticketFields['fields'] = json_encode($newFileds);
                $ticketFields['ticket_action_id'] = $ticketActionQuery->id;
                $ticketFieldQuery = $ticketQuery->ticketFiled()->create($ticketFields);
                /*******TicketFields*********/

                /*******TicketCategories*********/

                $category_id = $distribution->category_id;
                for ($i =1 ;$i<= $countLevels ;$i++)
                {
                    $cat = Category::where('id',$category_id)->first();
                    $categories['category_level_id'] = $cat->category_level_id;
                    $categories['category_id']     = $cat->id;

                    $categories2['category_level_id'] = $cat->category_level_id;
                    $categories2['category_id']     = $cat->id;
                    $categories['ticket_action_id'] = $ticketActionQuery->id;

                    $ticketQuery->ticketCategories()->create($categories);
                    $resource->resourceCategories()->create($categories2);

                    $category_id = $cat->parent_id;
                }
                /*******TicketCategories*********/

                /*******RunChannel*********/
                broadcast(new SystemNotify(
                    tenant('id'),
                    $distribution->department_id,
                    $ticketInfo['agent_id'],
                    'You have received a new ticket, Please access the ticket page',
                    $request->getHttpHost(),
                    $ticketQuery,
                    $ticketInfoQuery,
                ));
                /*******RunChannel*********/

                /******* check langauge *********/
                if ($request->language == "Arabic"){
                    $msgLang = "(Arabic) ";
                }elseif ($request->language == "English"){
                    $msgLang = "(English) ";
                }else{
                    $msgLang = "(English) ";
                }
                $department = Department::find($ticketAction['department_id']);

                /*******SendEmail*********/
                $emailScript = EmailScript::where('type', $msgLang.'New Ticket For 3th')->first();
                if ($emailScript){


                    if ($department) {
                        $users = $department->users;

                        foreach ($users as $user) {

                            $body = $emailScript->template;
                            $body = str_replace('$ticket-id', $ticketQuery->id,$body);
                            $body = str_replace('$channel', $ticketInfoQuery->channel,$body);
                            $body = str_replace('$type', $ticketInfoQuery->type,$body);
                            $body = str_replace('$department', $department->name,$body);
                            $body = str_replace('$agent-name',User::where('id',$ticketInfo['agent_id'])->first()->first_name ,$body);
                            $body = str_replace('$customer-name', $ticketCustomerQuery->display_name,$body);
                            $body = str_replace('$priority', $ticketInfoQuery->priority,$body);
                            $body = str_replace('$comment', $request->comment,$body);

                            $jsjs =json_decode($ticketFieldQuery->fields);
                            $jsjs = collect($jsjs);

                            foreach (FormField::get() as $field)
                            {
                                $body = str_replace("$".$field->slug, isset($jsjs[$field->slug]) ? $jsjs[$field->slug] : '',$body);

                            }


                            Mailer::send($user->email, "New Ticket #".$ticketQuery->id, $body ,null,null);

                        }
                    }
                }

                $emailScriptCustomer = EmailScript::where('type', $msgLang.'New Ticket For Customer')->first();
                if ($emailScriptCustomer)
                {
                    if (isset($request->customerEmail))
                    {
                        if ($request->customerEmail != null)
                        {

                            $body = $emailScript->template;
                            $body = str_replace('$ticket-id', $ticketQuery->id,$body);
                            $body = str_replace('$channel', $ticketInfoQuery->channel,$body);
                            $body = str_replace('$type', $ticketInfoQuery->type,$body);
                            $body = str_replace('$department', $department->name,$body);
                            $body = str_replace('$agent-name',User::where('id',$ticketInfo['agent_id'])->first()->first_name ,$body);
                            $body = str_replace('$customer-name', $ticketCustomerQuery->display_name,$body);
                            $body = str_replace('$priority', $ticketInfoQuery->priority,$body);
                            $body = str_replace('$comment', $request->comment,$body);

                            $jsjs =json_decode($ticketFieldQuery->fields);
                            $jsjs = collect($jsjs);

                            foreach (FormField::get() as $field)
                            {
                                $body = str_replace("$".$field->slug, isset($jsjs[$field->slug]) ? $jsjs[$field->slug] : '',$body);

                            }

                            Mailer::send($request->customerEmail, "New Ticket #".$ticketQuery->id, $body ,null,null);

                        }
                    }

                }

                $emailScriptEsclation = EmailScript::where('type', $msgLang.'Escalated Ticket For Uppers')->first();
                if ($emailScriptEsclation)
                {
                    $esc = Escalation::where('department_id',$department->id)->where('level',1)->get();

                    if ($esc)
                    {
                        foreach ($esc as $es)
                        {
                            $body = $emailScript->template;
                            $body = str_replace('$ticket-id', $ticketQuery->id,$body);
                            $body = str_replace('$channel', $ticketInfoQuery->channel,$body);
                            $body = str_replace('$type', $ticketInfoQuery->type,$body);
                            $body = str_replace('$department', $department->name,$body);
                            $body = str_replace('$agent-name',User::where('id',$ticketInfo['agent_id'])->first()->first_name ,$body);
                            $body = str_replace('$customer-name', $ticketCustomerQuery->display_name,$body);
                            $body = str_replace('$priority', $ticketInfoQuery->priority,$body);
                            $body = str_replace('$comment', $request->comment,$body);

                            $jsjs =json_decode($ticketFieldQuery->fields);
                            $jsjs = collect($jsjs);

                            foreach (FormField::get() as $field)
                            {
                                $body = str_replace("$".$field->slug, isset($jsjs[$field->slug]) ? $jsjs[$field->slug] : '',$body);

                            }

                            Mailer::send($es->email, "New Ticket #".$ticketQuery->id, $body ,null,null);
                        }

                    }

                }
                /*******SendEmail*********/

                /*******SendSMS*********/
                $smsScript = SmsScript::where('type', $msgLang.'New Ticket For 3th')->first();
                if ($smsScript){
                    $department = Department::find($ticketAction['department_id']);

                    if ($department) {
                        $users = $department->users;
                        foreach ($users as $user) {

                            $body = $emailScript->template;
                            $body = str_replace('$ticket-id', $ticketQuery->id,$body);
                            $body = str_replace('$channel', $ticketInfoQuery->channel,$body);
                            $body = str_replace('$type', $ticketInfoQuery->type,$body);
                            $body = str_replace('$department', $department->name,$body);
                            $body = str_replace('$agent-name',User::where('id',$ticketInfo['agent_id'])->first()->first_name ,$body);
                            $body = str_replace('$customer-name', $ticketCustomerQuery->display_name,$body);
                            $body = str_replace('$priority', $ticketInfoQuery->priority,$body);
                            $body = str_replace('$comment', $request->comment,$body);

                            $jsjs =json_decode($ticketFieldQuery->fields);
                            $jsjs = collect($jsjs);

                            foreach (FormField::get() as $field)
                            {
                                $body = str_replace("$".$field->slug, isset($jsjs[$field->slug]) ? $jsjs[$field->slug] : '',$body);

                            }

                            $data = Sms::send($user->mobile, $body);

                        }
                    }
                }

                $smsScriptCustomer = SmsScript::where('type', $msgLang.'New Ticket For Customer')->first();
                if ($smsScriptCustomer)
                {
                    if (isset($request->customerNumber))
                    {
                        if ($request->customerNumber != null)
                        {

                            $body = $emailScript->template;
                            $body = str_replace('$ticket-id', $ticketQuery->id,$body);
                            $body = str_replace('$channel', $ticketInfoQuery->channel,$body);
                            $body = str_replace('$type', $ticketInfoQuery->type,$body);
                            $body = str_replace('$department', $department->name,$body);
                            $body = str_replace('$agent-name',User::where('id',$ticketInfo['agent_id'])->first()->first_name ,$body);
                            $body = str_replace('$customer-name', $ticketCustomerQuery->display_name,$body);
                            $body = str_replace('$priority', $ticketInfoQuery->priority,$body);
                            $body = str_replace('$comment', $request->comment,$body);

                            $jsjs =json_decode($ticketFieldQuery->fields);
                            $jsjs = collect($jsjs);

                            foreach (FormField::get() as $field)
                            {
                                $body = str_replace("$".$field->slug, isset($jsjs[$field->slug]) ? $jsjs[$field->slug] : '',$body);

                            }

                            Sms::send($request->customerNumber, $body );

                        }
                    }
                }
                /*******SendSMS*********/


                /*******resourceFields*********/
                $resourceFields['fields'] = $request->formFields;
                $resourceFieldQuery = $resource->resourceFields()->create($resourceFields);
                /*******resourceFields*********/

                /*******resourceAction*********/
                $resourceAction['action']         = 'New Ticket';
                $resourceAction['user_id']        = auth('api')->id();
                $resourceAction['comment']        = $request->comment;

                $resourceActionQuery = $resource->resourceActions()->create($resourceAction);
                /*******resourceAction*********/


                $resource->update(['status'=>'New Ticket']);

                $queue = AgentQueue::where('platform',$resource->resourceInfo->channel)->where('user_id',auth('api')->id())->first();

                if ($queue)
                {
                    $old_queue = $queue->queue;
                    $queue->update(['queue'=>$old_queue-1]);
                }

            }
            else
            {

                /*******Ticket*********/
                $ticket['status']           = 'New';
                $ticket['department_id']    = $distribution->department_id;
                $ticket['resource_id']      = null;

                $ticketQuery = Ticket::create($ticket);

//                return response()->json($ticketQuery, 200);
                /*******Ticket*********/

                /*******TicketInfo*********/
                $ticketInfo['channel']      = $request->type;
                $ticketInfo['type']         = $request->typeFrom;
                $ticketInfo['priority']     = $request->priority;
                $ticketInfo['language']     = $request->language;
                $ticketInfo['country']      = $request->country;

                $countries = new \PragmaRX\Countries\Package\Countries();
                $flag = $countries->where('name.common', $request->country)->first();
                if ($flag) {
                    $flagIcon = isset($flag->flag['flag-icon']) ? $flag->flag['flag-icon'] : false;
                    if ($flagIcon) {
                        $ticketInfo['countryFlag'] =$flagIcon;
                    } else {
                        $ticketInfo['countryFlag'] ='';
                    }
                } else {
                    $ticketInfo['countryFlag'] ='';
                }
                $ticketInfo['agent_id']     = auth('api')->id();
                $ticketInfo['comment']      = $request->comment;

                $ticketInfoQuery = $ticketQuery->ticketInfo()->create($ticketInfo);
                /*******TicketInfo*********/

                /*******TicketCustomer*********/
                $ticketCustomer['username']         = 'Unknown';
                $ticketCustomer['display_name']      = 'Unknown';
                $ticketCustomer['mobile_number']      = "Unknown";

                foreach (json_decode($request->formFields) as $key => $value)
                {
                    $key = Str::slug($key);
                    if ($key == 'customer-name')
                    {
                        $ticketCustomer['username']         = $value;
                        $ticketCustomer['display_name']      = $value;
                    }
                }

                if (isset($request->customerEmail)){$ticketCustomer['username']   = $request->customerEmail;}
                if (isset($request->customerName)){$ticketCustomer['display_name']= $request->customerName;}
                if (isset($request->customerNumber)){$ticketCustomer['mobile_number']= $request->customerNumber;}


                $ticketCustomerQuery = $ticketQuery->ticketCustomer()->create($ticketCustomer);
                /*******TicketInfo*********/


                /*******TicketAction*********/
                $ticketAction['action']         = 'New Ticket';
                $ticketAction['user_id']        = auth('api')->id();
                $ticketAction['department_id']  = $distribution->department_id;
                $ticketAction['comment']        = $request->comment;
                $ticketActionQuery = $ticketQuery->ticketActions()->create($ticketAction);
                /*******TicketCustomer*********/

                /*******TicketFields*********/
                $ticketFields['fields'] = $request->formFields;
                $ticketFields['ticket_action_id'] = $ticketActionQuery->id;
                $ticketFieldQuery = $ticketQuery->ticketFiled()->create($ticketFields);
                /*******TicketFields*********/

                /*******TicketCategories*********/
                $category_id = $distribution->category_id;
                for ($i =1 ;$i<= $countLevels ;$i++)
                {
                    $cat = Category::where('id',$category_id)->first();
                    $categories['category_level_id'] = $cat->category_level_id;
                    $categories['category_id']     = $cat->id;
                    $categories['ticket_action_id'] = $ticketActionQuery->id;

                    $ticketQuery->ticketCategories()->create($categories);
                    $category_id = $cat->parent_id;
                }
                /*******TicketCategories*********/

                /*******RunChannel*********/
                broadcast(new SystemNotify(
                    tenant('id'),
                    $distribution->department_id,$ticketInfo['agent_id'],
                    'You have received a new ticket, Please access the ticket page',
                    $request->getHttpHost(),
                    $ticketQuery,
                    $ticketInfoQuery,
                ));
                /*******RunChannel*********/

                /******* check langauge *********/
                if ($request->language == "Arabic"){
                    $msgLang = "(Arabic) ";
                }elseif ($request->language == "English"){
                    $msgLang = "(English) ";
                }else{
                    $msgLang = "(English) ";
                }
                $department = Department::find($ticketAction['department_id']);

                /*******SendEmail*********/
                $emailScript = EmailScript::where('type', $msgLang.'New Ticket For 3th')->first();
                if ($emailScript){


                    if ($department) {
                        $users = $department->users;

                        foreach ($users as $user) {

                            $body = $emailScript->template;
                            $body = str_replace('$ticket-id', $ticketQuery->id,$body);
                            $body = str_replace('$channel', $ticketInfoQuery->channel,$body);
                            $body = str_replace('$type', $ticketInfoQuery->type,$body);
                            $body = str_replace('$department', $department->name,$body);
                            $body = str_replace('$agent-name',User::where('id',$ticketInfo['agent_id'])->first()->first_name ,$body);
                            $body = str_replace('$customer-name', $ticketCustomerQuery->display_name,$body);
                            $body = str_replace('$priority', $ticketInfoQuery->priority,$body);
                            $body = str_replace('$comment', $request->comment,$body);

                            $jsjs =json_decode($ticketFieldQuery->fields);
                            $jsjs = collect($jsjs);

                            foreach (FormField::get() as $field)
                            {
                                $body = str_replace("$".$field->slug, isset($jsjs[$field->slug]) ? $jsjs[$field->slug] : '',$body);

                            }


                            Mailer::send($user->email, "New Ticket #".$ticketQuery->id, $body ,null,null);

                        }
                    }
                }

                $emailScriptCustomer = EmailScript::where('type', $msgLang.'New Ticket For Customer')->first();
                if ($emailScriptCustomer)
                {
                    if (isset($request->customerEmail))
                    {
                        if ($request->customerEmail != null)
                        {

                            $body = $emailScript->template;
                            $body = str_replace('$ticket-id', $ticketQuery->id,$body);
                            $body = str_replace('$channel', $ticketInfoQuery->channel,$body);
                            $body = str_replace('$type', $ticketInfoQuery->type,$body);
                            $body = str_replace('$department', $department->name,$body);
                            $body = str_replace('$agent-name',User::where('id',$ticketInfo['agent_id'])->first()->first_name ,$body);
                            $body = str_replace('$customer-name', $ticketCustomerQuery->display_name,$body);
                            $body = str_replace('$priority', $ticketInfoQuery->priority,$body);
                            $body = str_replace('$comment', $request->comment,$body);

                            $jsjs =json_decode($ticketFieldQuery->fields);
                            $jsjs = collect($jsjs);

                            foreach (FormField::get() as $field)
                            {
                                $body = str_replace("$".$field->slug, isset($jsjs[$field->slug]) ? $jsjs[$field->slug] : '',$body);

                            }

                            Mailer::send($request->customerEmail, "New Ticket #".$ticketQuery->id, $body ,null,null);

                        }
                    }

                }

                $emailScriptEsclation = EmailScript::where('type', $msgLang.'Escalated Ticket For Uppers')->first();
                if ($emailScriptEsclation)
                {
                    $esc = Escalation::where('department_id',$department->id)->where('level',1)->get();

                    if ($esc)
                    {
                        foreach ($esc as $es)
                        {
                            $body = $emailScript->template;
                            $body = str_replace('$ticket-id', $ticketQuery->id,$body);
                            $body = str_replace('$channel', $ticketInfoQuery->channel,$body);
                            $body = str_replace('$type', $ticketInfoQuery->type,$body);
                            $body = str_replace('$department', $department->name,$body);
                            $body = str_replace('$agent-name',User::where('id',$ticketInfo['agent_id'])->first()->first_name ,$body);
                            $body = str_replace('$customer-name', $ticketCustomerQuery->display_name,$body);
                            $body = str_replace('$priority', $ticketInfoQuery->priority,$body);
                            $body = str_replace('$comment', $request->comment,$body);

                            $jsjs =json_decode($ticketFieldQuery->fields);
                            $jsjs = collect($jsjs);

                            foreach (FormField::get() as $field)
                            {
                                $body = str_replace("$".$field->slug, isset($jsjs[$field->slug]) ? $jsjs[$field->slug] : '',$body);

                            }

                            Mailer::send($es->email, "New Ticket #".$ticketQuery->id, $body ,null,null);
                        }

                    }

                }
                /*******SendEmail*********/

                /*******SendSMS*********/
                $smsScript = SmsScript::where('type', $msgLang.'New Ticket For 3th')->first();
                if ($smsScript){
                    $department = Department::find($ticketAction['department_id']);

                    if ($department) {
                        $users = $department->users;
                        foreach ($users as $user) {

                            $body = $emailScript->template;
                            $body = str_replace('$ticket-id', $ticketQuery->id,$body);
                            $body = str_replace('$channel', $ticketInfoQuery->channel,$body);
                            $body = str_replace('$type', $ticketInfoQuery->type,$body);
                            $body = str_replace('$department', $department->name,$body);
                            $body = str_replace('$agent-name',User::where('id',$ticketInfo['agent_id'])->first()->first_name ,$body);
                            $body = str_replace('$customer-name', $ticketCustomerQuery->display_name,$body);
                            $body = str_replace('$priority', $ticketInfoQuery->priority,$body);
                            $body = str_replace('$comment', $request->comment,$body);

                            $jsjs =json_decode($ticketFieldQuery->fields);
                            $jsjs = collect($jsjs);

                            foreach (FormField::get() as $field)
                            {
                                $body = str_replace("$".$field->slug, isset($jsjs[$field->slug]) ? $jsjs[$field->slug] : '',$body);

                            }

                            $data = Sms::send($user->mobile, $body);

                        }
                    }
                }

                $smsScriptCustomer = SmsScript::where('type', $msgLang.'New Ticket For Customer')->first();
                if ($smsScriptCustomer)
                {
                    if (isset($request->customerNumber))
                    {
                        if ($request->customerNumber != null)
                        {

                            $body = $emailScript->template;
                            $body = str_replace('$ticket-id', $ticketQuery->id,$body);
                            $body = str_replace('$channel', $ticketInfoQuery->channel,$body);
                            $body = str_replace('$type', $ticketInfoQuery->type,$body);
                            $body = str_replace('$department', $department->name,$body);
                            $body = str_replace('$agent-name',User::where('id',$ticketInfo['agent_id'])->first()->first_name ,$body);
                            $body = str_replace('$customer-name', $ticketCustomerQuery->display_name,$body);
                            $body = str_replace('$priority', $ticketInfoQuery->priority,$body);
                            $body = str_replace('$comment', $request->comment,$body);

                            $jsjs =json_decode($ticketFieldQuery->fields);
                            $jsjs = collect($jsjs);

                            foreach (FormField::get() as $field)
                            {
                                $body = str_replace("$".$field->slug, isset($jsjs[$field->slug]) ? $jsjs[$field->slug] : '',$body);

                            }

                            Sms::send($request->customerNumber, $body );

                        }
                    }
                }
                /*******SendSMS*********/

            }
            return response()->json(['message' => 'successfully created'], 200);
        }
        else
        {

            $distribution = Distribution::where('id',$request->formFieldsId)->first();

            if (!$distribution)
            {
                return response()->json(['errors' => true, 'message' => 'No form fields id found'], 200);
            }

            $countLevels        = $distribution->category->categoryLevel->level;



            /*******Ticket*********/
            $ticket['status']           = 'New';
            $ticket['department_id']    = $distribution->department_id;
            $ticket['resource_id']      = null;

            $ticketQuery = Ticket::create($ticket);



            if ($request->hasFile('attachment')) {
                // Retrieve all uploaded files with the key 'attachment'
                $files = $request->file('attachment');
                // Loop through each uploaded file
                foreach ($files as $file) {
                    // Generate a unique filename
                    $filename = rand(1, 100000) . strtotime(now()) . '.' . $file->getClientOriginalExtension();

                    // Get the MIME type of the uploaded file
                    $mime_type = $file->getClientMimeType();

                    // Store the file in the public disk
                    Storage::disk('public')->put('/ticket/' . $filename, file_get_contents($file));

                    // Create a record in the TicketFile model
                    TicketFile::create([
                        'path' => $filename,
                        'type' => $file->extension(),
                        'ticket_id' => $ticketQuery->id
                    ]);
                }
            }


            /*******Ticket*********/

            /*******TicketInfo*********/

            if ($request->type == "bot")
            {
                $ticketInfo['channel']      = 'Bot';
                $ticketInfo['type']         = 'Message';
            }elseif ($request->type == "api")
            {
                $ticketInfo['type']         = $request->typeFrom;
                $ticketInfo['channel']      = 'Api';
            }else
            {
                $ticketInfo['channel']      = 'Voice';
                $ticketInfo['type']         = 'Calls';
            }

            $ticketInfo['priority']     = $request->priority;
            $ticketInfo['language']     = $request->language;
            $ticketInfo['country']      = $request->country;
            $countries = new \PragmaRX\Countries\Package\Countries();
            $flag = $countries->where('name.common', $request->country)->first();
            if ($flag) {
                $flagIcon = isset($flag->flag['flag-icon']) ? $flag->flag['flag-icon'] : false;
                if ($flagIcon) {
                    $ticketInfo['countryFlag'] =$flagIcon;
                } else {
                    $ticketInfo['countryFlag'] ='';
                }
            } else {
                $ticketInfo['countryFlag'] ='';
            }
            $ticketInfo['agent_id']     = auth('api')->id();
            $ticketInfo['comment']      = $request->comment;

            $ticketInfoQuery = $ticketQuery->ticketInfo()->create($ticketInfo);
            /*******TicketInfo*********/

            /*******TicketCustomer*********/
            $ticketCustomer['username']         = 'Unknown';
            $ticketCustomer['display_name']      = 'Unknown';
            $ticketCustomer['mobile_number']      = "Unknown";

            foreach (json_decode($request->formFields) as $key => $value)
            {
                $key = Str::slug($key);
                if ($key == 'customer-name')
                {
                    $ticketCustomer['username']         = $value;
                    $ticketCustomer['display_name']      = $value;
                }
            }

            if (isset($request->customerEmail)){$ticketCustomer['username']         = $request->customerEmail;}
            if (isset($request->customerName)){$ticketCustomer['display_name']      = $request->customerName;}
            if (isset($request->customerNumber)){$ticketCustomer['mobile_number']   = $request->customerNumber;}

            $ticketCustomerQuery = $ticketQuery->ticketCustomer()->create($ticketCustomer);
            /*******TicketInfo*********/


            /*******TicketAction*********/
            $ticketAction['action']         = 'New Ticket';
            $ticketAction['user_id']        = auth('api')->id();
            $ticketAction['department_id']  = $distribution->department_id;
            $ticketAction['comment']        = $request->comment;
            $ticketActionQuery = $ticketQuery->ticketActions()->create($ticketAction);
            /*******TicketCustomer*********/

            /*******TicketFields*********/
            $ticketFields['fields'] = $request->formFields;

            $formFields = $request->formFields;

            $resultString=json_decode($formFields);

            $newFileds = [];
            foreach ($resultString as $key =>$value)
            {
                $newFileds[Str::slug($key)] = $value;
            }
            $ticketFields['fields'] = json_encode($newFileds);

            $ticketFields['ticket_action_id'] = $ticketActionQuery->id;
            $ticketFieldQuery = $ticketQuery->ticketFiled()->create($ticketFields);
            /*******TicketFields*********/

            /*******TicketCategories*********/
            $category_id = $distribution->category_id;
            for ($i =1 ;$i<= $countLevels ;$i++)
            {
                $cat = Category::where('id',$category_id)->first();
                $categories['category_level_id'] = $cat->category_level_id;
                $categories['category_id']     = $cat->id;
                $categories['ticket_action_id'] = $ticketActionQuery->id;

                $ticketQuery->ticketCategories()->create($categories);


                $category_id = $cat->parent_id;
            }
            /*******TicketCategories*********/

            /*******RunChannel*********/
            broadcast(new SystemNotify(
                tenant('id'),
                $distribution->department_id,$ticketInfo['agent_id'],
                'You have received a new ticket, Please access the ticket page',
                $request->getHttpHost(),
                $ticketQuery,
                $ticketInfoQuery,
            ));
            /*******RunChannel*********/

            /******* check langauge *********/
            if ($request->language == "Arabic"){
                $msgLang = "(Arabic) ";
            }elseif ($request->language == "English"){
                $msgLang = "(English) ";
            }else{
                $msgLang = "(English) ";
            }
            $department = Department::find($ticketAction['department_id']);

            /*******SendEmail*********/
            $emailScript = EmailScript::where('type', $msgLang.'New Ticket For 3th')->first();
            if ($emailScript){

                if ($department) {
                    $users = $department->users;

                    foreach ($users as $user) {

                        $body = $emailScript->template;
                        $body = str_replace('$ticket-id', $ticketQuery->id,$body);
                        $body = str_replace('$channel', $ticketInfoQuery->channel,$body);
                        $body = str_replace('$type', $ticketInfoQuery->type,$body);
                        $body = str_replace('$department', $department->name,$body);
                        $body = str_replace('$agent-name',User::where('id',$ticketInfo['agent_id'])->first()->first_name ,$body);
                        $body = str_replace('$customer-name', $ticketCustomerQuery->display_name,$body);
                        $body = str_replace('$priority', $ticketInfoQuery->priority,$body);
                        $body = str_replace('$comment', $request->comment,$body);

                        $jsjs =json_decode($ticketFieldQuery->fields);
                        $jsjs = collect($jsjs);

                        foreach (FormField::get() as $field)
                        {
                            $body = str_replace("$".$field->slug, isset($jsjs[$field->slug]) ? $jsjs[$field->slug] : '',$body);

                        }


                        Mailer::send($user->email, "New Ticket #".$ticketQuery->id, $body ,null,null);

                    }
                }
            }

            $emailScriptCustomer = EmailScript::where('type', $msgLang.'New Ticket For Customer')->first();
            if ($emailScriptCustomer)
            {
                if (isset($request->customerEmail))
                {
                    if ($request->customerEmail != null)
                    {

                        $body = $emailScriptCustomer->template;
                        $body = str_replace('$ticket-id', $ticketQuery->id,$body);
                        $body = str_replace('$channel', $ticketInfoQuery->channel,$body);
                        $body = str_replace('$type', $ticketInfoQuery->type,$body);
                        $body = str_replace('$department', $department->name,$body);
                        $body = str_replace('$agent-name',User::where('id',$ticketInfo['agent_id'])->first()->first_name ,$body);
                        $body = str_replace('$customer-name', $ticketCustomerQuery->display_name,$body);
                        $body = str_replace('$priority', $ticketInfoQuery->priority,$body);
                        $body = str_replace('$comment', $request->comment,$body);

                        $jsjs =json_decode($ticketFieldQuery->fields);
                        $jsjs = collect($jsjs);

                        foreach (FormField::get() as $field)
                        {
                            $body = str_replace("$".$field->slug, isset($jsjs[$field->slug]) ? $jsjs[$field->slug] : '',$body);

                        }

                        Mailer::send($request->customerEmail, "New Ticket #".$ticketQuery->id, $body ,null,null);

                    }
                }

            }

            $emailScriptEsclation = EmailScript::where('type', $msgLang.'Escalated Ticket For Uppers')->first();
            if ($emailScriptEsclation)
            {
                $esc = Escalation::where('department_id',$department->id)->where('level',1)->get();

                if ($esc)
                {
                    foreach ($esc as $es)
                    {
                        $body = $emailScriptEsclation->template;
                        $body = str_replace('$ticket-id', $ticketQuery->id,$body);
                        $body = str_replace('$channel', $ticketInfoQuery->channel,$body);
                        $body = str_replace('$type', $ticketInfoQuery->type,$body);
                        $body = str_replace('$department', $department->name,$body);
                        $body = str_replace('$agent-name',User::where('id',$ticketInfo['agent_id'])->first()->first_name ,$body);
                        $body = str_replace('$customer-name', $ticketCustomerQuery->display_name,$body);
                        $body = str_replace('$priority', $ticketInfoQuery->priority,$body);
                        $body = str_replace('$comment', $request->comment,$body);

                        $jsjs =json_decode($ticketFieldQuery->fields);
                        $jsjs = collect($jsjs);

                        foreach (FormField::get() as $field)
                        {
                            $body = str_replace("$".$field->slug, isset($jsjs[$field->slug]) ? $jsjs[$field->slug] : '',$body);

                        }

                        Mailer::send($es->email, "New Ticket #".$ticketQuery->id, $body ,null,null);
                    }

                }

            }
            /*******SendEmail*********/

            /*******SendSMS*********/
            $smsScript = SmsScript::where('type', $msgLang.'New Ticket For 3th')->first();
            if ($smsScript){
                $department = Department::find($ticketAction['department_id']);

                if ($department) {
                    $users = $department->users;
                    foreach ($users as $user) {

                        $body = $emailScript->template;
                        $body = str_replace('$ticket-id', $ticketQuery->id,$body);
                        $body = str_replace('$channel', $ticketInfoQuery->channel,$body);
                        $body = str_replace('$type', $ticketInfoQuery->type,$body);
                        $body = str_replace('$department', $department->name,$body);
                        $body = str_replace('$agent-name',User::where('id',$ticketInfo['agent_id'])->first()->first_name ,$body);
                        $body = str_replace('$customer-name', $ticketCustomerQuery->display_name,$body);
                        $body = str_replace('$priority', $ticketInfoQuery->priority,$body);
                        $body = str_replace('$comment', $request->comment,$body);

                        $jsjs =json_decode($ticketFieldQuery->fields);
                        $jsjs = collect($jsjs);

                        foreach (FormField::get() as $field)
                        {
                            $body = str_replace("$".$field->slug, isset($jsjs[$field->slug]) ? $jsjs[$field->slug] : '',$body);

                        }

                        $data = Sms::send($user->mobile, $body);

                    }
                }
            }

            $smsScriptCustomer = SmsScript::where('type', $msgLang.'New Ticket For Customer')->first();
            if ($smsScriptCustomer)
            {
                if (isset($request->customerNumber))
                {
                    if ($request->customerNumber != null)
                    {

                        $body = $emailScript->template;
                        $body = str_replace('$ticket-id', $ticketQuery->id,$body);
                        $body = str_replace('$channel', $ticketInfoQuery->channel,$body);
                        $body = str_replace('$type', $ticketInfoQuery->type,$body);
                        $body = str_replace('$department', $department->name,$body);
                        $body = str_replace('$agent-name',User::where('id',$ticketInfo['agent_id'])->first()->first_name ,$body);
                        $body = str_replace('$customer-name', $ticketCustomerQuery->display_name,$body);
                        $body = str_replace('$priority', $ticketInfoQuery->priority,$body);
                        $body = str_replace('$comment', $request->comment,$body);

                        $jsjs =json_decode($ticketFieldQuery->fields);
                        $jsjs = collect($jsjs);

                        foreach (FormField::get() as $field)
                        {
                            $body = str_replace("$".$field->slug, isset($jsjs[$field->slug]) ? $jsjs[$field->slug] : '',$body);

                        }

                        Sms::send($request->customerNumber, $body );

                    }
                }
            }
            /*******SendSMS*********/

            return response()->json(['message' => 'successfully created','ticketId'=>$ticketQuery->id], 200);
        }


    }

    public function create_migration(Request $request)
    {



        $validation = Validator::make($request->all(), [
            'type'          => 'required|in:voice,non-voice,bot,api',
            'typeFrom'      => 'required',
            'ticketId'      => 'required',
            'createdAt'      => 'required',
            'userId'      => 'required',
            'customerEmail' => 'nullable|email',
            'customerName'  => 'nullable',
            'customerNumber'=> 'nullable',
            'resourceId'    => Rule::requiredIf($request->type === 'non-voice'),
            'formFieldsId'  => 'required',
            'priority'      => 'nullable|in:High,Medium,Low,Critical',
            'language'      => 'nullable|in:English,Arabic',
//            'categoryIds' => 'required|array',
            'formFields' => 'required|json',
            'comment' => 'required',
        ]);



        if ($validation->fails()){
            return response()->json(['errors' => true, 'message' => $validation->errors()], 200);
        }

        if (isset($request->resourceId))
        {
            $resource = Resource::whereStatus('New')->find($request->resourceId);

            if (!$resource)
            {
                return response()->json(['errors' => true, 'message' => 'No resource found'], 200);
            }

            $distribution = Distribution::where('id',$request->formFieldsId)->first();

            if (!$distribution)
            {
                return response()->json(['errors' => true, 'message' => 'No form fields id found'], 200);
            }

            $countLevels        = $distribution->category->categoryLevel->level;

            if ($request->type == 'non-voice')
            {
                /*******Ticket*********/
                $ticket['status']           = 'New';
                $ticket['department_id']    = $distribution->department_id;
                $ticket['resource_id']      = $distribution->resource_id;
                $ticket['id']               = $request->ticketId;
                $ticket['created_at']      = $request->createdAt;
                $ticket['updated_at']      = $request->createdAt;
                $ticket['user_id']      = $request->userId;

                $ticketQuery = Ticket::create($ticket);
                /*******Ticket*********/

                /*******TicketInfo*********/
                $ticketInfo['channel']      = $resource->resourceInfo->channel;
                $ticketInfo['type']         = $resource->resourceInfo->type;
                $ticketInfo['priority']     = $request->priority;
                $ticketInfo['language']     = $request->language;
                $ticketInfo['country']      = $request->country;
                $countries = new \PragmaRX\Countries\Package\Countries();
                $flag = $countries->where('name.common', $request->country)->first();
                if ($flag) {
                    $flagIcon = isset($flag->flag['flag-icon']) ? $flag->flag['flag-icon'] : false;
                    if ($flagIcon) {
                        $ticketInfo['countryFlag'] =$flagIcon;
                    } else {
                        $ticketInfo['countryFlag'] ='';
                    }
                } else {
                    $ticketInfo['countryFlag'] ='';
                }
                $ticketInfo['agent_id']     = auth('api')->id();
                $ticketInfo['comment']      = $request->comment;
                $ticketInfo['created_at']      = $request->createdAt;
                $ticketInfo['updated_at']      = $request->createdAt;

                $ticketInfoQuery = $ticketQuery->ticketInfo()->create($ticketInfo);
                /*******TicketInfo*********/

                /*******TicketCustomer*********/
                $ticketCustomer['username']         = 'Unknown';
                $ticketCustomer['display_name']      = 'Unknown';
                $ticketCustomer['mobile_number']      = "Unknown";

                if (isset($request->customerEmail)){$ticketCustomer['username']         = $request->customerEmail;}
                if (isset($request->customerName)){$ticketCustomer['display_name']      = $request->customerName;}
                if (isset($request->customerNumber)){$ticketCustomer['mobile_number']   = $request->customerNumber;}

                $ticketCustomerQuery = $ticketQuery->ticketCustomer()->create($ticketCustomer);
                /*******TicketInfo*********/


                /*******TicketAction*********/
                $ticketAction['action']         = 'New Ticket';
                $ticketAction['user_id']        = $request->userId;
                $ticketAction['department_id']  = $distribution->department_id;
                $ticketAction['comment']        = $request->comment;
                $ticketAction['created_at']        = $request->createdAt;
                $ticketAction['updated_at']        = $request->createdAt;
                $ticketActionQuery = $ticketQuery->ticketActions()->create($ticketAction);
                /*******TicketCustomer*********/

                /*******TicketFields*********/
                $ticketFields['fields'] = $request->formFields;
                $formFields = $request->formFields;

                $resultString=json_decode($formFields);

                $newFileds = [];
                foreach ($resultString as $key =>$value)
                {
                    $newFileds[Str::slug($key)] = $value;
                }
                $ticketFields['fields'] = json_encode($newFileds);
                $ticketFields['ticket_action_id'] = $ticketActionQuery->id;
                $ticketFieldQuery = $ticketQuery->ticketFiled()->create($ticketFields);
                /*******TicketFields*********/

                /*******TicketCategories*********/

                $category_id = $distribution->category_id;
                for ($i =1 ;$i<= $countLevels ;$i++)
                {
                    $cat = Category::where('id',$category_id)->first();
                    $categories['category_level_id'] = $cat->category_level_id;
                    $categories['category_id']     = $cat->id;

                    $categories2['category_level_id'] = $cat->category_level_id;
                    $categories2['category_id']     = $cat->id;
                    $categories['ticket_action_id'] = $ticketActionQuery->id;

                    $ticketQuery->ticketCategories()->create($categories);
                    $resource->resourceCategories()->create($categories2);

                    $category_id = $cat->parent_id;
                }
                /*******TicketCategories*********/

                /*******RunChannel*********/
                broadcast(new SystemNotify(
                    tenant('id'),
                    $distribution->department_id,
                    $ticketInfo['agent_id'],
                    'You have received a new ticket, Please access the ticket page',
                    $request->getHttpHost(),
                    $ticketQuery,
                    $ticketInfoQuery,
                ));
                /*******RunChannel*********/

                /******* check langauge *********/
                if ($request->language == "Arabic"){
                    $msgLang = "(Arabic) ";
                }elseif ($request->language == "English"){
                    $msgLang = "(English) ";
                }else{
                    $msgLang = "(English) ";
                }
                $department = Department::find($ticketAction['department_id']);

                /*******SendEmail*********/
                $emailScript = EmailScript::where('type', $msgLang.'New Ticket For 3th')->first();
                if ($emailScript){


                    if ($department) {
                        $users = $department->users;

                        foreach ($users as $user) {

                            $body = $emailScript->template;
                            $body = str_replace('$ticket-id', $ticketQuery->id,$body);
                            $body = str_replace('$channel', $ticketInfoQuery->channel,$body);
                            $body = str_replace('$type', $ticketInfoQuery->type,$body);
                            $body = str_replace('$department', $department->name,$body);
                            $body = str_replace('$agent-name',User::where('id',$ticketInfo['agent_id'])->first()->first_name ,$body);
                            $body = str_replace('$customer-name', $ticketCustomerQuery->display_name,$body);
                            $body = str_replace('$priority', $ticketInfoQuery->priority,$body);
                            $body = str_replace('$comment', $request->comment,$body);

                            $jsjs =json_decode($ticketFieldQuery->fields);
                            $jsjs = collect($jsjs);

                            foreach (FormField::get() as $field)
                            {
                                $body = str_replace("$".$field->slug, isset($jsjs[$field->slug]) ? $jsjs[$field->slug] : '',$body);

                            }


                            Mailer::send($user->email, "New Ticket #".$ticketQuery->id, $body ,null,null);

                        }
                    }
                }

                $emailScriptCustomer = EmailScript::where('type', $msgLang.'New Ticket For Customer')->first();
                if ($emailScriptCustomer)
                {
                    if (isset($request->customerEmail))
                    {
                        if ($request->customerEmail != null)
                        {

                            $body = $emailScript->template;
                            $body = str_replace('$ticket-id', $ticketQuery->id,$body);
                            $body = str_replace('$channel', $ticketInfoQuery->channel,$body);
                            $body = str_replace('$type', $ticketInfoQuery->type,$body);
                            $body = str_replace('$department', $department->name,$body);
                            $body = str_replace('$agent-name',User::where('id',$ticketInfo['agent_id'])->first()->first_name ,$body);
                            $body = str_replace('$customer-name', $ticketCustomerQuery->display_name,$body);
                            $body = str_replace('$priority', $ticketInfoQuery->priority,$body);
                            $body = str_replace('$comment', $request->comment,$body);

                            $jsjs =json_decode($ticketFieldQuery->fields);
                            $jsjs = collect($jsjs);

                            foreach (FormField::get() as $field)
                            {
                                $body = str_replace("$".$field->slug, isset($jsjs[$field->slug]) ? $jsjs[$field->slug] : '',$body);

                            }

                            Mailer::send($request->customerEmail, "New Ticket #".$ticketQuery->id, $body ,null,null);

                        }
                    }

                }

                $emailScriptEsclation = EmailScript::where('type', $msgLang.'Escalated Ticket For Uppers')->first();
                if ($emailScriptEsclation)
                {
                    $esc = Escalation::where('department_id',$department->id)->where('level',1)->get();

                    if ($esc)
                    {
                        foreach ($esc as $es)
                        {
                            $body = $emailScript->template;
                            $body = str_replace('$ticket-id', $ticketQuery->id,$body);
                            $body = str_replace('$channel', $ticketInfoQuery->channel,$body);
                            $body = str_replace('$type', $ticketInfoQuery->type,$body);
                            $body = str_replace('$department', $department->name,$body);
                            $body = str_replace('$agent-name',User::where('id',$ticketInfo['agent_id'])->first()->first_name ,$body);
                            $body = str_replace('$customer-name', $ticketCustomerQuery->display_name,$body);
                            $body = str_replace('$priority', $ticketInfoQuery->priority,$body);
                            $body = str_replace('$comment', $request->comment,$body);

                            $jsjs =json_decode($ticketFieldQuery->fields);
                            $jsjs = collect($jsjs);

                            foreach (FormField::get() as $field)
                            {
                                $body = str_replace("$".$field->slug, isset($jsjs[$field->slug]) ? $jsjs[$field->slug] : '',$body);

                            }

                            Mailer::send($es->email, "New Ticket #".$ticketQuery->id, $body ,null,null);
                        }

                    }

                }
                /*******SendEmail*********/

                /*******SendSMS*********/
                $smsScript = SmsScript::where('type', $msgLang.'New Ticket For 3th')->first();
                if ($smsScript){
                    $department = Department::find($ticketAction['department_id']);

                    if ($department) {
                        $users = $department->users;
                        foreach ($users as $user) {

                            $body = $emailScript->template;
                            $body = str_replace('$ticket-id', $ticketQuery->id,$body);
                            $body = str_replace('$channel', $ticketInfoQuery->channel,$body);
                            $body = str_replace('$type', $ticketInfoQuery->type,$body);
                            $body = str_replace('$department', $department->name,$body);
                            $body = str_replace('$agent-name',User::where('id',$ticketInfo['agent_id'])->first()->first_name ,$body);
                            $body = str_replace('$customer-name', $ticketCustomerQuery->display_name,$body);
                            $body = str_replace('$priority', $ticketInfoQuery->priority,$body);
                            $body = str_replace('$comment', $request->comment,$body);

                            $jsjs =json_decode($ticketFieldQuery->fields);
                            $jsjs = collect($jsjs);

                            foreach (FormField::get() as $field)
                            {
                                $body = str_replace("$".$field->slug, isset($jsjs[$field->slug]) ? $jsjs[$field->slug] : '',$body);

                            }

                            $data = Sms::send($user->mobile, $body);

                        }
                    }
                }

                $smsScriptCustomer = SmsScript::where('type', $msgLang.'New Ticket For Customer')->first();
                if ($smsScriptCustomer)
                {
                    if (isset($request->customerNumber))
                    {
                        if ($request->customerNumber != null)
                        {

                            $body = $emailScript->template;
                            $body = str_replace('$ticket-id', $ticketQuery->id,$body);
                            $body = str_replace('$channel', $ticketInfoQuery->channel,$body);
                            $body = str_replace('$type', $ticketInfoQuery->type,$body);
                            $body = str_replace('$department', $department->name,$body);
                            $body = str_replace('$agent-name',User::where('id',$ticketInfo['agent_id'])->first()->first_name ,$body);
                            $body = str_replace('$customer-name', $ticketCustomerQuery->display_name,$body);
                            $body = str_replace('$priority', $ticketInfoQuery->priority,$body);
                            $body = str_replace('$comment', $request->comment,$body);

                            $jsjs =json_decode($ticketFieldQuery->fields);
                            $jsjs = collect($jsjs);

                            foreach (FormField::get() as $field)
                            {
                                $body = str_replace("$".$field->slug, isset($jsjs[$field->slug]) ? $jsjs[$field->slug] : '',$body);

                            }

                            Sms::send($request->customerNumber, $body );

                        }
                    }
                }
                /*******SendSMS*********/


                /*******resourceFields*********/
                $resourceFields['fields'] = $request->formFields;
                $resourceFieldQuery = $resource->resourceFields()->create($resourceFields);
                /*******resourceFields*********/

                /*******resourceAction*********/
                $resourceAction['action']         = 'New Ticket';
                $resourceAction['user_id']        = $request->userId;
                $resourceAction['comment']        = $request->comment;

                $resourceActionQuery = $resource->resourceActions()->create($resourceAction);
                /*******resourceAction*********/


                $resource->update(['status'=>'New Ticket']);

                $queue = AgentQueue::where('platform',$resource->resourceInfo->channel)->where('user_id',auth('api')->id())->first();

                if ($queue)
                {
                    $old_queue = $queue->queue;
                    $queue->update(['queue'=>$old_queue-1]);
                }

            }
            else
            {

                /*******Ticket*********/
                $ticket['status']           = 'New';
                $ticket['department_id']    = $distribution->department_id;
                $ticket['resource_id']      = null;
                $ticket['user_id']      = $request->userId;

                $ticketQuery = Ticket::create($ticket);

//                return response()->json($ticketQuery, 200);
                /*******Ticket*********/

                /*******TicketInfo*********/
                $ticketInfo['channel']      = $request->type;
                $ticketInfo['type']         = $request->typeFrom;
                $ticketInfo['priority']     = $request->priority;
                $ticketInfo['language']     = $request->language;
                $ticketInfo['country']      = $request->country;

                $countries = new \PragmaRX\Countries\Package\Countries();
                $flag = $countries->where('name.common', $request->country)->first();
                if ($flag) {
                    $flagIcon = isset($flag->flag['flag-icon']) ? $flag->flag['flag-icon'] : false;
                    if ($flagIcon) {
                        $ticketInfo['countryFlag'] =$flagIcon;
                    } else {
                        $ticketInfo['countryFlag'] ='';
                    }
                } else {
                    $ticketInfo['countryFlag'] ='';
                }
                $ticketInfo['agent_id']     = $request->userId;
                $ticketInfo['comment']      = $request->comment;

                $ticketInfoQuery = $ticketQuery->ticketInfo()->create($ticketInfo);
                /*******TicketInfo*********/

                /*******TicketCustomer*********/
                $ticketCustomer['username']         = 'Unknown';
                $ticketCustomer['display_name']      = 'Unknown';
                $ticketCustomer['mobile_number']      = "Unknown";

                foreach (json_decode($request->formFields) as $key => $value)
                {
                    $key = Str::slug($key);
                    if ($key == 'customer-name')
                    {
                        $ticketCustomer['username']         = $value;
                        $ticketCustomer['display_name']      = $value;
                    }
                }

                if (isset($request->customerEmail)){$ticketCustomer['username']   = $request->customerEmail;}
                if (isset($request->customerName)){$ticketCustomer['display_name']= $request->customerName;}
                if (isset($request->customerNumber)){$ticketCustomer['mobile_number']= $request->customerNumber;}


                $ticketCustomerQuery = $ticketQuery->ticketCustomer()->create($ticketCustomer);
                /*******TicketInfo*********/


                /*******TicketAction*********/
                $ticketAction['action']         = 'New Ticket';
                $ticketAction['user_id']        = $request->userId;
                $ticketAction['department_id']  = $distribution->department_id;
                $ticketAction['comment']        = $request->comment;
                $ticketActionQuery = $ticketQuery->ticketActions()->create($ticketAction);
                /*******TicketCustomer*********/

                /*******TicketFields*********/
                $ticketFields['fields'] = $request->formFields;
                $ticketFields['ticket_action_id'] = $ticketActionQuery->id;
                $ticketFieldQuery = $ticketQuery->ticketFiled()->create($ticketFields);
                /*******TicketFields*********/

                /*******TicketCategories*********/
                $category_id = $distribution->category_id;
                for ($i =1 ;$i<= $countLevels ;$i++)
                {
                    $cat = Category::where('id',$category_id)->first();
                    $categories['category_level_id'] = $cat->category_level_id;
                    $categories['category_id']     = $cat->id;
                    $categories['ticket_action_id'] = $ticketActionQuery->id;

                    $ticketQuery->ticketCategories()->create($categories);
                    $category_id = $cat->parent_id;
                }
                /*******TicketCategories*********/

                /*******RunChannel*********/
                broadcast(new SystemNotify(
                    tenant('id'),
                    $distribution->department_id,$ticketInfo['agent_id'],
                    'You have received a new ticket, Please access the ticket page',
                    $request->getHttpHost(),
                    $ticketQuery,
                    $ticketInfoQuery,
                ));
                /*******RunChannel*********/

                /******* check langauge *********/
                if ($request->language == "Arabic"){
                    $msgLang = "(Arabic) ";
                }elseif ($request->language == "English"){
                    $msgLang = "(English) ";
                }else{
                    $msgLang = "(English) ";
                }
                $department = Department::find($ticketAction['department_id']);

                /*******SendEmail*********/
                $emailScript = EmailScript::where('type', $msgLang.'New Ticket For 3th')->first();
                if ($emailScript){


                    if ($department) {
                        $users = $department->users;

                        foreach ($users as $user) {

                            $body = $emailScript->template;
                            $body = str_replace('$ticket-id', $ticketQuery->id,$body);
                            $body = str_replace('$channel', $ticketInfoQuery->channel,$body);
                            $body = str_replace('$type', $ticketInfoQuery->type,$body);
                            $body = str_replace('$department', $department->name,$body);
                            $body = str_replace('$agent-name',User::where('id',$ticketInfo['agent_id'])->first()->first_name ,$body);
                            $body = str_replace('$customer-name', $ticketCustomerQuery->display_name,$body);
                            $body = str_replace('$priority', $ticketInfoQuery->priority,$body);
                            $body = str_replace('$comment', $request->comment,$body);

                            $jsjs =json_decode($ticketFieldQuery->fields);
                            $jsjs = collect($jsjs);

                            foreach (FormField::get() as $field)
                            {
                                $body = str_replace("$".$field->slug, isset($jsjs[$field->slug]) ? $jsjs[$field->slug] : '',$body);

                            }


                            Mailer::send($user->email, "New Ticket #".$ticketQuery->id, $body ,null,null);

                        }
                    }
                }

                $emailScriptCustomer = EmailScript::where('type', $msgLang.'New Ticket For Customer')->first();
                if ($emailScriptCustomer)
                {
                    if (isset($request->customerEmail))
                    {
                        if ($request->customerEmail != null)
                        {

                            $body = $emailScript->template;
                            $body = str_replace('$ticket-id', $ticketQuery->id,$body);
                            $body = str_replace('$channel', $ticketInfoQuery->channel,$body);
                            $body = str_replace('$type', $ticketInfoQuery->type,$body);
                            $body = str_replace('$department', $department->name,$body);
                            $body = str_replace('$agent-name',User::where('id',$ticketInfo['agent_id'])->first()->first_name ,$body);
                            $body = str_replace('$customer-name', $ticketCustomerQuery->display_name,$body);
                            $body = str_replace('$priority', $ticketInfoQuery->priority,$body);
                            $body = str_replace('$comment', $request->comment,$body);

                            $jsjs =json_decode($ticketFieldQuery->fields);
                            $jsjs = collect($jsjs);

                            foreach (FormField::get() as $field)
                            {
                                $body = str_replace("$".$field->slug, isset($jsjs[$field->slug]) ? $jsjs[$field->slug] : '',$body);

                            }

                            Mailer::send($request->customerEmail, "New Ticket #".$ticketQuery->id, $body ,null,null);

                        }
                    }

                }

                $emailScriptEsclation = EmailScript::where('type', $msgLang.'Escalated Ticket For Uppers')->first();
                if ($emailScriptEsclation)
                {
                    $esc = Escalation::where('department_id',$department->id)->where('level',1)->get();

                    if ($esc)
                    {
                        foreach ($esc as $es)
                        {
                            $body = $emailScript->template;
                            $body = str_replace('$ticket-id', $ticketQuery->id,$body);
                            $body = str_replace('$channel', $ticketInfoQuery->channel,$body);
                            $body = str_replace('$type', $ticketInfoQuery->type,$body);
                            $body = str_replace('$department', $department->name,$body);
                            $body = str_replace('$agent-name',User::where('id',$ticketInfo['agent_id'])->first()->first_name ,$body);
                            $body = str_replace('$customer-name', $ticketCustomerQuery->display_name,$body);
                            $body = str_replace('$priority', $ticketInfoQuery->priority,$body);
                            $body = str_replace('$comment', $request->comment,$body);

                            $jsjs =json_decode($ticketFieldQuery->fields);
                            $jsjs = collect($jsjs);

                            foreach (FormField::get() as $field)
                            {
                                $body = str_replace("$".$field->slug, isset($jsjs[$field->slug]) ? $jsjs[$field->slug] : '',$body);

                            }

                            Mailer::send($es->email, "New Ticket #".$ticketQuery->id, $body ,null,null);
                        }

                    }

                }
                /*******SendEmail*********/

                /*******SendSMS*********/
                $smsScript = SmsScript::where('type', $msgLang.'New Ticket For 3th')->first();
                if ($smsScript){
                    $department = Department::find($ticketAction['department_id']);

                    if ($department) {
                        $users = $department->users;
                        foreach ($users as $user) {

                            $body = $emailScript->template;
                            $body = str_replace('$ticket-id', $ticketQuery->id,$body);
                            $body = str_replace('$channel', $ticketInfoQuery->channel,$body);
                            $body = str_replace('$type', $ticketInfoQuery->type,$body);
                            $body = str_replace('$department', $department->name,$body);
                            $body = str_replace('$agent-name',User::where('id',$ticketInfo['agent_id'])->first()->first_name ,$body);
                            $body = str_replace('$customer-name', $ticketCustomerQuery->display_name,$body);
                            $body = str_replace('$priority', $ticketInfoQuery->priority,$body);
                            $body = str_replace('$comment', $request->comment,$body);

                            $jsjs =json_decode($ticketFieldQuery->fields);
                            $jsjs = collect($jsjs);

                            foreach (FormField::get() as $field)
                            {
                                $body = str_replace("$".$field->slug, isset($jsjs[$field->slug]) ? $jsjs[$field->slug] : '',$body);

                            }

                            $data = Sms::send($user->mobile, $body);

                        }
                    }
                }

                $smsScriptCustomer = SmsScript::where('type', $msgLang.'New Ticket For Customer')->first();
                if ($smsScriptCustomer)
                {
                    if (isset($request->customerNumber))
                    {
                        if ($request->customerNumber != null)
                        {

                            $body = $emailScript->template;
                            $body = str_replace('$ticket-id', $ticketQuery->id,$body);
                            $body = str_replace('$channel', $ticketInfoQuery->channel,$body);
                            $body = str_replace('$type', $ticketInfoQuery->type,$body);
                            $body = str_replace('$department', $department->name,$body);
                            $body = str_replace('$agent-name',User::where('id',$ticketInfo['agent_id'])->first()->first_name ,$body);
                            $body = str_replace('$customer-name', $ticketCustomerQuery->display_name,$body);
                            $body = str_replace('$priority', $ticketInfoQuery->priority,$body);
                            $body = str_replace('$comment', $request->comment,$body);

                            $jsjs =json_decode($ticketFieldQuery->fields);
                            $jsjs = collect($jsjs);

                            foreach (FormField::get() as $field)
                            {
                                $body = str_replace("$".$field->slug, isset($jsjs[$field->slug]) ? $jsjs[$field->slug] : '',$body);

                            }

                            Sms::send($request->customerNumber, $body );

                        }
                    }
                }
                /*******SendSMS*********/

            }
            return response()->json(['message' => 'successfully created'], 200);
        }
        else
        {

            $distribution = Distribution::where('id',$request->formFieldsId)->first();

            if (!$distribution)
            {
                return response()->json(['errors' => true, 'message' => 'No form fields id found'], 200);
            }

            $countLevels        = $distribution->category->categoryLevel->level;



            /*******Ticket*********/
            $ticket['status']           = 'New';
            $ticket['department_id']    = $distribution->department_id;
            $ticket['resource_id']      = null;
            $ticket['id']               = $request->ticketId;
            $ticket['created_at']      = $request->createdAt;
            $ticket['updated_at']      = $request->createdAt;
            $ticket['user_id']      = $request->userId;

            $ticketQuery = Ticket::create($ticket);



            if ($request->hasFile('attachment')) {
                // Retrieve all uploaded files with the key 'attachment'
                $files = $request->file('attachment');
                // Loop through each uploaded file
                foreach ($files as $file) {
                    // Generate a unique filename
                    $filename = rand(1, 100000) . strtotime(now()) . '.' . $file->getClientOriginalExtension();

                    // Get the MIME type of the uploaded file
                    $mime_type = $file->getClientMimeType();

                    // Store the file in the public disk
                    Storage::disk('public')->put('/ticket/' . $filename, file_get_contents($file));

                    // Create a record in the TicketFile model
                    TicketFile::create([
                        'path' => $filename,
                        'type' => $file->extension(),
                        'ticket_id' => $ticketQuery->id
                    ]);
                }
            }


            /*******Ticket*********/

            /*******TicketInfo*********/

            if ($request->type == "bot")
            {
                $ticketInfo['channel']      = 'Bot';
                $ticketInfo['type']         = 'Message';
            }elseif ($request->type == "api")
            {
                $ticketInfo['type']         = $request->typeFrom;
                $ticketInfo['channel']      = 'Api';
            }else
            {
                $ticketInfo['channel']      = 'Voice';
                $ticketInfo['type']         = 'Calls';
            }

            $ticketInfo['priority']     = $request->priority;
            $ticketInfo['language']     = $request->language;
            $ticketInfo['country']      = $request->country;
            $countries = new \PragmaRX\Countries\Package\Countries();
            $flag = $countries->where('name.common', $request->country)->first();
            if ($flag) {
                $flagIcon = isset($flag->flag['flag-icon']) ? $flag->flag['flag-icon'] : false;
                if ($flagIcon) {
                    $ticketInfo['countryFlag'] =$flagIcon;
                } else {
                    $ticketInfo['countryFlag'] ='';
                }
            } else {
                $ticketInfo['countryFlag'] ='';
            }
            $ticketInfo['agent_id']     = $request->userId;
            $ticketInfo['comment']      = $request->comment;
            $ticketInfo['created_at']      = $request->createdAt;
            $ticketInfo['updated_at']      = $request->createdAt;

            $ticketInfoQuery = $ticketQuery->ticketInfo()->create($ticketInfo);
            /*******TicketInfo*********/

            /*******TicketCustomer*********/
            $ticketCustomer['username']         = 'Unknown';
            $ticketCustomer['display_name']      = 'Unknown';
            $ticketCustomer['mobile_number']      = "Unknown";

            foreach (json_decode($request->formFields) as $key => $value)
            {
                $key = Str::slug($key);
                if ($key == 'customer-name')
                {
                    $ticketCustomer['username']         = $value;
                    $ticketCustomer['display_name']      = $value;
                }
            }

            if (isset($request->customerEmail)){$ticketCustomer['username']         = $request->customerEmail;}
            if (isset($request->customerName)){$ticketCustomer['display_name']      = $request->customerName;}
            if (isset($request->customerNumber)){$ticketCustomer['mobile_number']   = $request->customerNumber;}

            $ticketCustomerQuery = $ticketQuery->ticketCustomer()->create($ticketCustomer);
            /*******TicketInfo*********/


            /*******TicketAction*********/
            $ticketAction['action']         = 'New Ticket';
            $ticketAction['user_id']        = $request->userId;
            $ticketAction['department_id']  = $distribution->department_id;
            $ticketAction['comment']        = $request->comment;
            $ticketAction['created_at']      = $request->createdAt;
            $ticketAction['updated_at']      = $request->createdAt;
            $ticketActionQuery = $ticketQuery->ticketActions()->create($ticketAction);
            /*******TicketCustomer*********/

            /*******TicketFields*********/
            $ticketFields['fields'] = $request->formFields;

            $formFields = $request->formFields;

            $resultString=json_decode($formFields);

            $newFileds = [];
            foreach ($resultString as $key =>$value)
            {
                $newFileds[Str::slug($key)] = $value;
            }
            $ticketFields['fields'] = json_encode($newFileds);

            $ticketFields['ticket_action_id'] = $ticketActionQuery->id;
            $ticketFieldQuery = $ticketQuery->ticketFiled()->create($ticketFields);
            /*******TicketFields*********/

            /*******TicketCategories*********/
            $category_id = $distribution->category_id;
            for ($i =1 ;$i<= $countLevels ;$i++)
            {
                $cat = Category::where('id',$category_id)->first();
                $categories['category_level_id'] = $cat->category_level_id;
                $categories['category_id']     = $cat->id;
                $categories['ticket_action_id'] = $ticketActionQuery->id;

                $ticketQuery->ticketCategories()->create($categories);


                $category_id = $cat->parent_id;
            }
            /*******TicketCategories*********/

            /*******RunChannel*********/
            broadcast(new SystemNotify(
                tenant('id'),
                $distribution->department_id,$ticketInfo['agent_id'],
                'You have received a new ticket, Please access the ticket page',
                $request->getHttpHost(),
                $ticketQuery,
                $ticketInfoQuery,
            ));
            /*******RunChannel*********/

            /******* check langauge *********/
            if ($request->language == "Arabic"){
                $msgLang = "(Arabic) ";
            }elseif ($request->language == "English"){
                $msgLang = "(English) ";
            }else{
                $msgLang = "(English) ";
            }
            $department = Department::find($ticketAction['department_id']);

            /*******SendEmail*********/
            $emailScript = EmailScript::where('type', $msgLang.'New Ticket For 3th')->first();
            if ($emailScript){

                if ($department) {
                    $users = $department->users;

                    foreach ($users as $user) {

                        $body = $emailScript->template;
                        $body = str_replace('$ticket-id', $ticketQuery->id,$body);
                        $body = str_replace('$channel', $ticketInfoQuery->channel,$body);
                        $body = str_replace('$type', $ticketInfoQuery->type,$body);
                        $body = str_replace('$department', $department->name,$body);
                        $body = str_replace('$agent-name',User::where('id',$ticketInfo['agent_id'])->first()->first_name ,$body);
                        $body = str_replace('$customer-name', $ticketCustomerQuery->display_name,$body);
                        $body = str_replace('$priority', $ticketInfoQuery->priority,$body);
                        $body = str_replace('$comment', $request->comment,$body);

                        $jsjs =json_decode($ticketFieldQuery->fields);
                        $jsjs = collect($jsjs);

                        foreach (FormField::get() as $field)
                        {
                            $body = str_replace("$".$field->slug, isset($jsjs[$field->slug]) ? $jsjs[$field->slug] : '',$body);

                        }


                        Mailer::send($user->email, "New Ticket #".$ticketQuery->id, $body ,null,null);

                    }
                }
            }

            $emailScriptCustomer = EmailScript::where('type', $msgLang.'New Ticket For Customer')->first();
            if ($emailScriptCustomer)
            {
                if (isset($request->customerEmail))
                {
                    if ($request->customerEmail != null)
                    {

                        $body = $emailScriptCustomer->template;
                        $body = str_replace('$ticket-id', $ticketQuery->id,$body);
                        $body = str_replace('$channel', $ticketInfoQuery->channel,$body);
                        $body = str_replace('$type', $ticketInfoQuery->type,$body);
                        $body = str_replace('$department', $department->name,$body);
                        $body = str_replace('$agent-name',User::where('id',$ticketInfo['agent_id'])->first()->first_name ,$body);
                        $body = str_replace('$customer-name', $ticketCustomerQuery->display_name,$body);
                        $body = str_replace('$priority', $ticketInfoQuery->priority,$body);
                        $body = str_replace('$comment', $request->comment,$body);

                        $jsjs =json_decode($ticketFieldQuery->fields);
                        $jsjs = collect($jsjs);

                        foreach (FormField::get() as $field)
                        {
                            $body = str_replace("$".$field->slug, isset($jsjs[$field->slug]) ? $jsjs[$field->slug] : '',$body);

                        }

                        Mailer::send($request->customerEmail, "New Ticket #".$ticketQuery->id, $body ,null,null);

                    }
                }

            }

            $emailScriptEsclation = EmailScript::where('type', $msgLang.'Escalated Ticket For Uppers')->first();
            if ($emailScriptEsclation)
            {
                $esc = Escalation::where('department_id',$department->id)->where('level',1)->get();

                if ($esc)
                {
                    foreach ($esc as $es)
                    {
                        $body = $emailScriptEsclation->template;
                        $body = str_replace('$ticket-id', $ticketQuery->id,$body);
                        $body = str_replace('$channel', $ticketInfoQuery->channel,$body);
                        $body = str_replace('$type', $ticketInfoQuery->type,$body);
                        $body = str_replace('$department', $department->name,$body);
                        $body = str_replace('$agent-name',User::where('id',$ticketInfo['agent_id'])->first()->first_name ,$body);
                        $body = str_replace('$customer-name', $ticketCustomerQuery->display_name,$body);
                        $body = str_replace('$priority', $ticketInfoQuery->priority,$body);
                        $body = str_replace('$comment', $request->comment,$body);

                        $jsjs =json_decode($ticketFieldQuery->fields);
                        $jsjs = collect($jsjs);

                        foreach (FormField::get() as $field)
                        {
                            $body = str_replace("$".$field->slug, isset($jsjs[$field->slug]) ? $jsjs[$field->slug] : '',$body);

                        }

                        Mailer::send($es->email, "New Ticket #".$ticketQuery->id, $body ,null,null);
                    }

                }

            }
            /*******SendEmail*********/

            /*******SendSMS*********/
            $smsScript = SmsScript::where('type', $msgLang.'New Ticket For 3th')->first();
            if ($smsScript){
                $department = Department::find($ticketAction['department_id']);

                if ($department) {
                    $users = $department->users;
                    foreach ($users as $user) {

                        $body = $emailScript->template;
                        $body = str_replace('$ticket-id', $ticketQuery->id,$body);
                        $body = str_replace('$channel', $ticketInfoQuery->channel,$body);
                        $body = str_replace('$type', $ticketInfoQuery->type,$body);
                        $body = str_replace('$department', $department->name,$body);
                        $body = str_replace('$agent-name',User::where('id',$ticketInfo['agent_id'])->first()->first_name ,$body);
                        $body = str_replace('$customer-name', $ticketCustomerQuery->display_name,$body);
                        $body = str_replace('$priority', $ticketInfoQuery->priority,$body);
                        $body = str_replace('$comment', $request->comment,$body);

                        $jsjs =json_decode($ticketFieldQuery->fields);
                        $jsjs = collect($jsjs);

                        foreach (FormField::get() as $field)
                        {
                            $body = str_replace("$".$field->slug, isset($jsjs[$field->slug]) ? $jsjs[$field->slug] : '',$body);

                        }

                        $data = Sms::send($user->mobile, $body);

                    }
                }
            }

            $smsScriptCustomer = SmsScript::where('type', $msgLang.'New Ticket For Customer')->first();
            if ($smsScriptCustomer)
            {
                if (isset($request->customerNumber))
                {
                    if ($request->customerNumber != null)
                    {

                        $body = $emailScript->template;
                        $body = str_replace('$ticket-id', $ticketQuery->id,$body);
                        $body = str_replace('$channel', $ticketInfoQuery->channel,$body);
                        $body = str_replace('$type', $ticketInfoQuery->type,$body);
                        $body = str_replace('$department', $department->name,$body);
                        $body = str_replace('$agent-name',User::where('id',$ticketInfo['agent_id'])->first()->first_name ,$body);
                        $body = str_replace('$customer-name', $ticketCustomerQuery->display_name,$body);
                        $body = str_replace('$priority', $ticketInfoQuery->priority,$body);
                        $body = str_replace('$comment', $request->comment,$body);

                        $jsjs =json_decode($ticketFieldQuery->fields);
                        $jsjs = collect($jsjs);

                        foreach (FormField::get() as $field)
                        {
                            $body = str_replace("$".$field->slug, isset($jsjs[$field->slug]) ? $jsjs[$field->slug] : '',$body);

                        }

                        Sms::send($request->customerNumber, $body );

                    }
                }
            }
            /*******SendSMS*********/

            return response()->json(['message' => 'successfully created','ticketId'=>$ticketQuery->id], 200);
        }


    }

    public function resolve(Request $request)
    {

        $validation = Validator::make($request->all(), [
            'ticketId' => 'required',
            'comment' => 'required',
        ]);

        if ($validation->fails()){
            return response()->json(['errors' => true, 'message' => $validation->errors()], 200);
        }

        $resource = Ticket::whereId($request->ticketId)->first();


        if ($attachmentData = $request->file('attachment')) {

            $filename = rand(1, 100000) . strtotime(now()) . '.' . $attachmentData->getClientOriginalExtension();

            $attachment = $filename;
            // Get the MIME type of the uploaded file
            $mime_type = $attachmentData->getClientMimeType();

            Storage::disk('public')->put('/ticket/' . $filename, file_get_contents($attachmentData));

            TicketFile::create([
                'path'=>$filename,
                'type'=>$attachmentData->extension(),
                'ticket_id'=>$resource->id
            ]);
        }


        if (!$resource)
        {
            return response()->json(['errors' => true, 'message' => 'No ticket found'], 200);
        }

        $email = $resource->ticketCustomer->username;
        $validator = Validator::make(['email' => $email], [
            'email' => ['email'],
        ]);

        if($validator->fails()) {} else {
            $messageDear = "Dear ".$resource->ticketCustomer->display_name;
            $message = "Kindly note that your ticket with id: " . $resource->id . " has been resolved.";
            Mail::to($email)->bcc('<EMAIL>')->send(
                new NewTicketActionNotify($messageDear,$message,$resource,$resource->ticketInfo));
        }

        if ($resource->ticketInfo->channel != 'Bot' || $resource->ticketInfo->channel != 'Api')
        {


            $ticket['status']           = 'Resolved';
            $ticket['department_id']    = 0;
            $ticket['user_id']          = $resource->ticketInfo->agent_id;


            $ticket = $resource;

            $ticketAction['action']         = 'Resolved';

            if ($ticket) {
                if ($ticket->ticketInfo->channel != 'Voice')
                {
                    $ticket->update([
                        'status' => 'Resolved',
                        'department_id' => 0,
                        'user_id' => $ticket->ticketInfo->agent_id,
                    ]);

                    $ticket->ticketInfo->update(['comment'=>$request->comment]);
                }else{


                    if ($resource->ticketInfo->ticket->status == "Pending")
                    {
                        $ticketAction['action']         = 'Resolved';
                        $ticket->update([
                            'status' => 'Resolved',
                            'department_id' => 0,
                            'user_id' => $ticket->ticketInfo->agent_id,
                        ]);
                    }else
                    {
                        $ticketAction['action']         = 'Pending';
                        $ticket->update([
                            'status' => 'Pending',
                            'department_id' => 0,
                            'user_id' => $ticket->ticketInfo->agent_id,
                        ]);
                    }
//                    $ticketAction['action']         = 'Pending';
//                    $ticket->update([
//                        'status' => 'Resolved',
//                        'department_id' => 0,
//                        'user_id' => $ticket->ticketInfo->agent_id,
//                    ]);

                    $ticket->ticketInfo->update(['comment'=>$request->comment]);
                }

            }



            $ticketAction['user_id']        = auth()->id();
            $ticketAction['department_id']  = 0;
            $ticketAction['comment']        = $request->comment;

            $ticketActionQuery = $ticket->ticketActions()->create($ticketAction);



            if ($ticket->ticketInfo->channel != 'Voice' && $ticket->ticketInfo->channel != "Api" && $ticket->ticketInfo->channel != "Bot"){
                $resour = Resource::find($ticket->resource_id);

                if ($resour){
                    $resour->update([
                        'status' => 'Pending',
                    ]);

                    $resourceAction['action']         = 'Resolved';
                    $resourceAction['user_id']        = auth()->id();
                    $resourceAction['comment']        = $request->comment;

                    $resourceActionQuery = $resour->resourceActions()->create($resourceAction);
                }

            }
        }
        else
        {

            $ticket['status']           = 'Closed';
            $ticket['department_id']    = 0;
            $ticket['user_id']          = $resource->ticketInfo->agent_id;


            $ticket = Ticket::find($resource->ticketInfo->ticket_id);

            if ($ticket) {
                $ticket->update([
                    'status' => 'Closed',
                    'department_id' => 0,
                    'user_id' => $resource->ticketInfo->agent_id,
                ]);
            }


            $ticketAction['action']         = 'Resolved';
            $ticketAction['user_id']        = auth()->id();
            $ticketAction['department_id']  = 0;
            $ticketAction['comment']        = $request->comment;

            $ticketActionQuery = $ticket->ticketActions()->create($ticketAction);


        }

        return response()->json(['message' => 'successfully resolved'], 200);

    }

    public function resolve_migration(Request $request)
    {

        $validation = Validator::make($request->all(), [
            'ticketId' => 'required',
            'comment' => 'required',
            'createdAt'      => 'required',
        ]);

        if ($validation->fails()){
            return response()->json(['errors' => true, 'message' => $validation->errors()], 200);
        }

        $resource = Ticket::whereId($request->ticketId)->first();


        if ($attachmentData = $request->file('attachment')) {

            $filename = rand(1, 100000) . strtotime(now()) . '.' . $attachmentData->getClientOriginalExtension();

            $attachment = $filename;
            // Get the MIME type of the uploaded file
            $mime_type = $attachmentData->getClientMimeType();

            Storage::disk('public')->put('/ticket/' . $filename, file_get_contents($attachmentData));

            TicketFile::create([
                'path'=>$filename,
                'type'=>$attachmentData->extension(),
                'ticket_id'=>$resource->id
            ]);
        }


        if (!$resource)
        {
            return response()->json(['errors' => true, 'message' => 'No ticket found'], 200);
        }

        $email = $resource->ticketCustomer->username;
        $validator = Validator::make(['email' => $email], [
            'email' => ['email'],
        ]);

        if($validator->fails()) {} else {
            $messageDear = "Dear ".$resource->ticketCustomer->display_name;
            $message = "Kindly note that your ticket with id: " . $resource->id . " has been resolved.";
            Mail::to($email)->bcc('<EMAIL>')->send(
                new NewTicketActionNotify($messageDear,$message,$resource,$resource->ticketInfo));
        }

        if ($resource->ticketInfo->channel != 'Bot' || $resource->ticketInfo->channel != 'Api')
        {


            $ticket['status']           = 'Resolved';
            $ticket['department_id']    = 0;
            $ticket['user_id']          = $resource->ticketInfo->agent_id;
            $ticket['created_at']      = $request->createdAt;
            $ticket['updated_at']      = $request->createdAt;


            $ticket = $resource;

            $ticketAction['action']         = 'Resolved';

            if ($ticket) {
                if ($ticket->ticketInfo->channel != 'Voice')
                {
                    $ticket->update([
                        'status' => 'Resolved',
                        'department_id' => 0,
                        'user_id' => $ticket->ticketInfo->agent_id,

                    ]);

                    $ticket->ticketInfo->update(['comment'=>$request->comment]);
                }else{


                    if ($resource->ticketInfo->ticket->status == "Pending")
                    {
                        $ticketAction['action']         = 'Resolved';
                        $ticket->update([
                            'status' => 'Resolved',
                            'department_id' => 0,
                            'user_id' => $ticket->ticketInfo->agent_id,

                        ]);
                    }else
                    {
                        $ticketAction['action']         = 'Resolved';
                        $ticket->update([
                            'status' => 'Resolved',
                            'department_id' => 5,
                            'user_id' => $ticket->ticketInfo->agent_id,
                        ]);
                    }
//                    $ticketAction['action']         = 'Pending';
//                    $ticket->update([
//                        'status' => 'Resolved',
//                        'department_id' => 0,
//                        'user_id' => $ticket->ticketInfo->agent_id,
//                    ]);

                    $ticket->ticketInfo->update(['comment'=>$request->comment]);
                }

            }



            $ticketAction['user_id']        = auth()->id();
            $ticketAction['department_id']  = 5;
            $ticketAction['comment']        = $request->comment;
            $ticketAction['created_at']      = $request->createdAt;
            $ticketAction['updated_at']      = $request->createdAt;

            $ticketActionQuery = $ticket->ticketActions()->create($ticketAction);



            if ($ticket->ticketInfo->channel != 'Voice' && $ticket->ticketInfo->channel != "Api" && $ticket->ticketInfo->channel != "Bot"){
                $resour = Resource::find($ticket->resource_id);

                if ($resour){
                    $resour->update([
                        'status' => 'Pending',
                    ]);

                    $resourceAction['action']         = 'Resolved';
                    $resourceAction['user_id']        = auth()->id();
                    $resourceAction['comment']        = $request->comment;

                    $resourceActionQuery = $resour->resourceActions()->create($resourceAction);
                }

            }
        }
        else
        {

            $ticket['status']           = 'Closed';
            $ticket['department_id']    = 0;
            $ticket['user_id']          = $resource->ticketInfo->agent_id;

            $ticket['created_at']      = $request->createdAt;
            $ticket['updated_at']      = $request->createdAt;

            $ticket = Ticket::find($resource->ticketInfo->ticket_id);

            if ($ticket) {
                $ticket->update([
                    'status' => 'Closed',
                    'department_id' => 0,
                    'user_id' => $resource->ticketInfo->agent_id,
                ]);
            }


            $ticketAction['action']         = 'Resolved';
            $ticketAction['user_id']        = auth()->id();
            $ticketAction['department_id']  = 0;
            $ticketAction['comment']        = $request->comment;
            $ticketAction['created_at']      = $request->createdAt;
            $ticketAction['updated_at']      = $request->createdAt;

            $ticketActionQuery = $ticket->ticketActions()->create($ticketAction);


        }

        return response()->json(['message' => 'successfully resolved'], 200);

    }

    public function close(Request $request)
    {
        $validation = Validator::make($request->all(), [
            'ticketId' => 'required',
            'comment' => 'required',
        ]);

        if ($validation->fails()){
            return response()->json(['errors' => true, 'message' => $validation->errors()], 200);
        }

        $resource = Ticket::whereId($request->ticketId)->first();

        if (!$resource)
        {
            return response()->json(['errors' => true, 'message' => 'No ticket found'], 200);
        }

        $email = $resource->ticketCustomer->username;
        $validator = Validator::make(['email' => $email], [
            'email' => ['email'],
        ]);

        if($validator->fails()) {} else {
            $messageDear = "Dear ".$resource->ticketCustomer->display_name;
            $message = "Kindly note that your ticket with id: " . $resource->id . " has been closed.";
            Mail::to($email)->bcc('<EMAIL>')->send(
                new NewTicketActionNotify($messageDear,$message,$resource,$resource->ticketInfo));
        }

        if ($resource->ticketInfo->channel != 'Bot')
        {


            $ticket['status']           = 'Closed';
            $ticket['department_id']    = 0;
            $ticket['user_id']          = $resource->ticketInfo->agent_id;


            $ticket = $resource;

            $ticketAction['action']         = 'Closed';

            if ($ticket) {
                if ($ticket->ticketInfo->channel != 'Voice')
                {
                    $ticket->update([
                        'status' => 'Closed',
                        'department_id' => 0,
                        'user_id' => $ticket->ticketInfo->agent_id,
                    ]);

                    $ticket->ticketInfo->update(['comment'=>$request->comment]);
                }else{
                    $ticketAction['action']         = 'Closed';
                    $ticket->update([
                        'status' => 'Closed',
                        'department_id' => 0,
                        'user_id' => $ticket->ticketInfo->agent_id,
                    ]);

                    $ticket->ticketInfo->update(['comment'=>$request->comment]);
                }

            }



            $ticketAction['user_id']        = auth()->id();
            $ticketAction['department_id']  = 0;
            $ticketAction['comment']        = $request->comment;

            $ticketActionQuery = $ticket->ticketActions()->create($ticketAction);


        }
        else
        {

            $ticket['status']           = 'Closed';
            $ticket['department_id']    = 0;
            $ticket['user_id']          = $resource->ticketInfo->agent_id;


            $ticket = Ticket::find($resource->ticketInfo->ticket_id);

            if ($ticket) {
                $ticket->update([
                    'status' => 'Closed',
                    'department_id' => 0,
                    'user_id' => $resource->ticketInfo->agent_id,
                ]);
            }


            $ticketAction['action']         = 'Closed';
            $ticketAction['user_id']        = auth()->id();
            $ticketAction['department_id']  = 0;
            $ticketAction['comment']        = $request->comment;

            $ticketActionQuery = $ticket->ticketActions()->create($ticketAction);


        }

        if ($attachmentData = $request->file('attachment')) {

            $filename = rand(1, 100000) . strtotime(now()) . '.' . $attachmentData->getClientOriginalExtension();

            $attachment = $filename;
            // Get the MIME type of the uploaded file
            $mime_type = $attachmentData->getClientMimeType();

            Storage::disk('public')->put('/ticket/' . $filename, file_get_contents($attachmentData));

            TicketFile::create([
                'path'=>$filename,
                'type'=>$attachmentData->extension(),
                'ticket_id'=>$ticket->id
            ]);
        }

        return response()->json(['message' => 'successfully closed'], 200);
    }

    public function close_migration(Request $request)
    {
        $validation = Validator::make($request->all(), [
            'ticketId' => 'required',
            'comment' => 'required',
            'createdAt'      => 'required',
            'userId'      => 'required',
        ]);

        if ($validation->fails()){
            return response()->json(['errors' => true, 'message' => $validation->errors()], 200);
        }

        $resource = Ticket::whereId($request->ticketId)->first();

        if (!$resource)
        {
            return response()->json(['errors' => true, 'message' => 'No ticket found'], 200);
        }

        $email = $resource->ticketCustomer->username;
        $validator = Validator::make(['email' => $email], [
            'email' => ['email'],
        ]);

        if($validator->fails()) {} else {
            $messageDear = "Dear ".$resource->ticketCustomer->display_name;
            $message = "Kindly note that your ticket with id: " . $resource->id . " has been closed.";
            Mail::to($email)->bcc('<EMAIL>')->send(
                new NewTicketActionNotify($messageDear,$message,$resource,$resource->ticketInfo));
        }

        if ($resource->ticketInfo->channel != 'Bot')
        {


            $ticket['status']           = 'Closed';
            $ticket['department_id']    = 0;
            $ticket['user_id']          = $request->userId;
            $ticket['created_at']       = $request->createdAt;
            $ticket['updated_at']       = $request->createdAt;

            $ticket = $resource;

            $ticketAction['action']         = 'Closed';

            if ($ticket) {
                if ($ticket->ticketInfo->channel != 'Voice')
                {
                    $ticket->update([
                        'status' => 'Closed',
                        'department_id' => 0,
                        'user_id' => $ticket->ticketInfo->agent_id,
                    ]);

                    $ticket->ticketInfo->update(['comment'=>$request->comment]);
                }else{
                    $ticketAction['action']         = 'Closed';
                    $ticket->update([
                        'status' => 'Closed',
                        'department_id' => 0,
                        'user_id' => $ticket->ticketInfo->agent_id,
                    ]);

                    $ticket->ticketInfo->update(['comment'=>$request->comment]);
                }

            }



            $ticketAction['user_id']        = $request->userId;
            $ticketAction['department_id']  = 0;
            $ticketAction['comment']        = $request->comment;
            $ticketAction['created_at']      = $request->createdAt;
            $ticketAction['updated_at']      = $request->createdAt;


            $ticketActionQuery = $ticket->ticketActions()->create($ticketAction);


        }
        else
        {

            $ticket['status']           = 'Closed';
            $ticket['department_id']    = 0;
            $ticket['user_id']          = $request->userId;
            $ticket['created_at']      = $request->createdAt;
            $ticket['updated_at']      = $request->createdAt;


            $ticket = Ticket::find($resource->ticketInfo->ticket_id);

            if ($ticket) {
                $ticket->update([
                    'status' => 'Closed',
                    'department_id' => 0,
                    'user_id' => $resource->ticketInfo->agent_id,
                ]);
            }


            $ticketAction['action']         = 'Closed';
            $ticketAction['user_id']        = $request->userId;
            $ticketAction['department_id']  = 0;
            $ticketAction['comment']        = $request->comment;
            $ticketAction['created_at']      = $request->createdAt;
            $ticketAction['updated_at']      = $request->createdAt;

            $ticketActionQuery = $ticket->ticketActions()->create($ticketAction);


        }

        if ($attachmentData = $request->file('attachment')) {

            $filename = rand(1, 100000) . strtotime(now()) . '.' . $attachmentData->getClientOriginalExtension();

            $attachment = $filename;
            // Get the MIME type of the uploaded file
            $mime_type = $attachmentData->getClientMimeType();

            Storage::disk('public')->put('/ticket/' . $filename, file_get_contents($attachmentData));

            TicketFile::create([
                'path'=>$filename,
                'type'=>$attachmentData->extension(),
                'ticket_id'=>$ticket->id
            ]);
        }

        return response()->json(['message' => 'successfully closed'], 200);
    }

    public function redirect_migration(Request $request)
    {

        $validation = Validator::make($request->all(), [
            'ticketId' => 'required',
            'formFieldsId' => 'required',
            'createdAt'      => 'required',
            'comment' => 'required',
        ]);

        if ($validation->fails()){
            return response()->json(['errors' => true, 'message' => $validation->errors()], 200);
        }

        $resource = Ticket::whereId($request->ticketId)->first();

        if (!$resource)
        {
            return response()->json(['errors' => true, 'message' => 'No ticket found'], 200);
        }


        $distribution = Distribution::where('id',$request->formFieldsId)->first();

        if (!$distribution)
        {
            return response()->json(['errors' => true, 'message' => 'No form fields id found'], 200);
        }

        $countLevels        = $distribution->category->categoryLevel->level;

        $ticket['status']           = 'Redirect';
        $ticket['department_id']    = $distribution->department_id;


        $ticket = $resource;
        if ($ticket) {
            $ticket->update([
                'status' => 'Redirect',
                'user_id' => null,
                'department_id' => $distribution->department_id,
            ]);
        }


        if ($attachmentData = $request->file('attachment')) {

            $filename = rand(1, 100000) . strtotime(now()) . '.' . $attachmentData->getClientOriginalExtension();

            $attachment = $filename;
            // Get the MIME type of the uploaded file
            $mime_type = $attachmentData->getClientMimeType();

            Storage::disk('public')->put('/ticket/' . $filename, file_get_contents($attachmentData));

            TicketFile::create([
                'path'=>$filename,
                'type'=>$attachmentData->extension(),
                'ticket_id'=>$ticket->id
            ]);
        }

        $ticket->ticketInfo->update(['comment'=>$request->comment]);


        $ticketAction['action']         = 'Redirect';
        $ticketAction['user_id']        = auth()->id();
        $ticketAction['department_id']  = $distribution->department_id;
        $ticketAction['comment']        = $request->comment;
        $ticketAction['created_at']      = $request->createdAt;
        $ticketAction['updated_at']      = $request->createdAt;

        $ticketActionQuery = $ticket->ticketActions()->create($ticketAction);


        $category_id = $distribution->category_id;

        for ($i =1 ;$i<= $countLevels ;$i++)
        {
            $cat = Category::where('id',$category_id)->first();
            $categories['category_level_id'] = $cat->category_level_id;
            $categories['category_id']     = $cat->id;
            $categories['ticket_action_id'] = $ticketActionQuery->id;

            $ticketCatQuery = $ticket->ticketCategories()->create($categories);
            $category_id = $cat->parent_id;
        }


        return response()->json(['message' => 'successfully redirected'], 200);
    }

    public function redirect(Request $request)
    {

        $validation = Validator::make($request->all(), [
            'ticketId' => 'required',
            'formFieldsId' => 'required',
            'comment' => 'required',
        ]);

        if ($validation->fails()){
            return response()->json(['errors' => true, 'message' => $validation->errors()], 200);
        }

        $resource = Ticket::whereId($request->ticketId)->first();

        if (!$resource)
        {
            return response()->json(['errors' => true, 'message' => 'No ticket found'], 200);
        }


        $distribution = Distribution::where('id',$request->formFieldsId)->first();

        if (!$distribution)
        {
            return response()->json(['errors' => true, 'message' => 'No form fields id found'], 200);
        }

        $countLevels        = $distribution->category->categoryLevel->level;

        $ticket['status']           = 'Redirect';
        $ticket['department_id']    = $distribution->department_id;


        $ticket = $resource;
        if ($ticket) {
            $ticket->update([
                'status' => 'Redirect',
                'user_id' => null,
                'department_id' => $distribution->department_id,
            ]);
        }


        if ($attachmentData = $request->file('attachment')) {

            $filename = rand(1, 100000) . strtotime(now()) . '.' . $attachmentData->getClientOriginalExtension();

            $attachment = $filename;
            // Get the MIME type of the uploaded file
            $mime_type = $attachmentData->getClientMimeType();

            Storage::disk('public')->put('/ticket/' . $filename, file_get_contents($attachmentData));

            TicketFile::create([
                'path'=>$filename,
                'type'=>$attachmentData->extension(),
                'ticket_id'=>$ticket->id
            ]);
        }

        $ticket->ticketInfo->update(['comment'=>$request->comment]);


        $ticketAction['action']         = 'Redirect';
        $ticketAction['user_id']        = auth()->id();
        $ticketAction['department_id']  = $distribution->department_id;
        $ticketAction['comment']        = $request->comment;

        $ticketActionQuery = $ticket->ticketActions()->create($ticketAction);


        $category_id = $distribution->category_id;

        for ($i =1 ;$i<= $countLevels ;$i++)
        {
            $cat = Category::where('id',$category_id)->first();
            $categories['category_level_id'] = $cat->category_level_id;
            $categories['category_id']     = $cat->id;
            $categories['ticket_action_id'] = $ticketActionQuery->id;

            $ticketCatQuery = $ticket->ticketCategories()->create($categories);
            $category_id = $cat->parent_id;
        }

        broadcast(new SystemNotify(
            tenant('id'),
            $distribution->department_id,auth()->id(),
            'You have received a redirected ticket, Please access the ticket page',
            $request->getHttpHost(),
            $ticket,
            $ticket->ticketInfo
        ));

        $department = Department::find($ticket['department_id']);

        if ($department)
        {
            $users = $department->users;

            foreach($users as $user)
            {
                Mail::to($user->email)->bcc('<EMAIL>')->send(new NewTicketNotify(auth()->user()->first_name,$ticket->ticketCustomer->display_name,
                    $request->getHttpHost(),
                    $ticket,
                    $ticket->ticketInfo));

            }
        }

        return response()->json(['message' => 'successfully redirected'], 200);
    }

    public function reopen(Request $request)
    {
        $validation = Validator::make($request->all(), [
            'ticketId' => 'required',
            'formFieldsId' => 'required',
            'comment' => 'required',
        ]);

        if ($validation->fails()){
            return response()->json(['errors' => true, 'message' => $validation->errors()], 200);
        }

        $resource = Ticket::whereId($request->ticketId)->first();

        if (!$resource)
        {
            return response()->json(['errors' => true, 'message' => 'No ticket found'], 200);
        }

        $email = $resource->ticketCustomer->username;
        $validator = Validator::make(['email' => $email], [
            'email' => ['email'],
        ]);

        if($validator->fails()) {} else {
            $messageDear = "Dear ".$resource->ticketCustomer->display_name;
            $message = "Kindly note that your ticket with id: " . $resource->id . " has been reopen.";
            Mail::to($email)->bcc('<EMAIL>')->send(
                new NewTicketActionNotify($messageDear,$message,$resource,$resource->ticketInfo));
        }


        $distribution = Distribution::where('id',$request->formFieldsId)->first();

        if (!$distribution)
        {
            return response()->json(['errors' => true, 'message' => 'No form fields id found'], 200);
        }

        $countLevels        = $distribution->category->categoryLevel->level;

        $ticket['status']           = 'Reopen';
        $ticket['department_id']    = $distribution->department_id;


        $ticket = $resource;

        if ($ticket) {
            $ticket->update([
                'status' => 'Reopen',
                'user_id' => null,
                'department_id' => $distribution->department_id,
            ]);


        }

        $ticket->ticketInfo->update(['comment'=>$request->comment]);

        if ($attachmentData = $request->file('attachment')) {

            $filename = rand(1, 100000) . strtotime(now()) . '.' . $attachmentData->getClientOriginalExtension();

            $attachment = $filename;
            // Get the MIME type of the uploaded file
            $mime_type = $attachmentData->getClientMimeType();

            Storage::disk('public')->put('/ticket/' . $filename, file_get_contents($attachmentData));

            TicketFile::create([
                'path'=>$filename,
                'type'=>$attachmentData->extension(),
                'ticket_id'=>$ticket->id
            ]);
        }
        $ticketAction['action']         = 'Reopen';
        $ticketAction['user_id']        = auth()->id();
        $ticketAction['department_id']  = $distribution->department_id;
        $ticketAction['comment']        = $request->comment;

        $ticketActionQuery = $ticket->ticketActions()->create($ticketAction);


        $category_id = $distribution->category_id;

        for ($i =1 ;$i<= $countLevels ;$i++)
        {
            $cat = Category::where('id',$category_id)->first();
            $categories['category_level_id'] = $cat->category_level_id;
            $categories['category_id']       = $cat->id;
            $categories['ticket_action_id']  = $ticketActionQuery->id;

            $ticketCatQuery = $ticket->ticketCategories()->create($categories);
            $category_id = $cat->parent_id;
        }

        broadcast(new SystemNotify(
            tenant('id'),
            $distribution->department_id,
            auth()->id(),
            'You have received a reopen ticket, Please access the ticket page',
            $request->getHttpHost(),
            $ticket,
            $ticket->ticketInfo,
        ));

        $department = Department::find($ticket['department_id']);

        if ($department)
        {
            $users = $department->users;

            foreach($users as $user)
            {
                Mail::to($user->email)->bcc('<EMAIL>')->send(new NewTicketNotify(auth()->user()->first_name,$ticket->ticketCustomer->display_name,
                    $request->getHttpHost(),
                    $ticket,
                    $ticket->ticketInfo,));

            }
        }

        return response()->json(['message' => 'successfully reopened'], 200);

    }

}
