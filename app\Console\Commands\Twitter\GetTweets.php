<?php

namespace App\Console\Commands\Twitter;

use App\Models\Tenant;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GetTweets extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'twitter:tweets';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
//            Log::info("get_tweets command Success");
            $tenants = Tenant::with('domains')->get();

            foreach ($tenants as $tenant) {
                foreach ($tenant->domains as $domain) {
                    try {
                        Http::withOptions(['verify' => false])->get('http://' . $domain->domain . '/webhook/getTweets');
//                        Log::info('Successfully fetched tweets for domain', ['domain' => $domain->domain]);
                    } catch (\Exception $e) {
                        Log::error('Failed to fetch tweets for domain', [
                            'domain' => $domain->domain,
                            'message' => $e->getMessage(),
                        ]);
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error('get_tweets command failed', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }
}
