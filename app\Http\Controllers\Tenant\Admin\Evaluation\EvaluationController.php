<?php

namespace App\Http\Controllers\Tenant\Admin\Evaluation;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class EvaluationController extends Controller
{
   
    
     /**
     * Handle the incoming request.
     */
    public function index()
    {
        return view('tenant.admin.evaluation.index');
    }

    public function createGroup($id)
    {
        return view('tenant.admin.evaluation.createGroup',compact('id'));
    }
    public function createQuestion($evaluation_id, $group_id)
    {

        return view('tenant.admin.evaluation.createQuestion', compact('evaluation_id', 'group_id'));
    }
    public function editQuestion($evaluation_id, $group_id ,$question_id)
    {

        return view('tenant.admin.evaluation.editQuestion', compact('evaluation_id', 'group_id','question_id'));
    }
    public function createFormBuilder($evaluation_id){

        return view('tenant.admin.evaluation.createFormBuilder', compact('evaluation_id'));

    }
    public function submitEvaluationForm($evaluation_id){

        return view('tenant.admin.evaluation.submitEvaluationForm', compact('evaluation_id'));

    }
    public function report(){

        return view('tenant.admin.evaluation.report');

    }
    public function reportAnswers($submit_id){

        return view('tenant.admin.evaluation.reportAnswers', compact('submit_id'));
    }

}
