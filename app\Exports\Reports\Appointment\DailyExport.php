<?php

namespace App\Exports\Reports\Appointment;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class DailyExport implements FromCollection, WithMapping, WithHeadings
{
    /**
    * @return \Illuminate\Support\Collection
    */

    public $appointments;

    public function __construct($appointments)
    {
        $this->appointments = $appointments;

    }

    public function collection()
    {
        return $this->appointments;
    }


    public function map($appointment): array
    {
        return [
            $appointment->id,
            $appointment->ticket_id,
            ucfirst($appointment->createdFor->first_name)." " .ucfirst($appointment->createdFor->last_name),
            $appointment->createdFor->email,
            ucfirst($appointment->status),
        ];
    }

    public function headings(): array
    {
        return [
            'Appointment ID',
            'Ticket ID',
            'Customer Name',
            'Customer Email',
            'Status',
        ];
    }

}
