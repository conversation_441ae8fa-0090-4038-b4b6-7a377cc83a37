<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {

        // $schedule->command('clear-compiled')->dailyAt('00:00');
        // $schedule->command('cache:clear')->dailyAt('00:00');
        // $schedule->command('view:clear')->dailyAt('00:00');
        // $schedule->command('view:cache')->dailyAt('00:00');
        // $schedule->command('config:clear')->dailyAt('00:00');
        // $schedule->command('config:cache')->dailyAt('00:00');
        // $schedule->command('route:clear')->dailyAt('00:00');
        // $schedule->command('event:clear')->dailyAt('00:00');


        $schedule->command('youtube:refresh-token')->everyFiveMinutes();
        $schedule->command('google:refresh-token')->everyFiveMinutes();
//        $schedule->command('meta:refresh-token')->everyFiveMinutes();
        $schedule->command('linkedin:refresh-token')->daily();

        $schedule->command('calls:get')->everyThirtyMinutes();
        $schedule->command('calls:fetch-maqsam-calls')->everyThirtyMinutes();

        $schedule->command('escalation:run')->hourly();
        $schedule->command('escalation:new')->everyFifteenMinutes();
        $schedule->command('social-escalation:new')->everyFiveMinutes();

        $schedule->command('agent:logout')->everyFiveMinutes();
        $schedule->command('google:reviews')->everyFiveMinutes();
        // $schedule->command('google:reviews')->everyFiveMinutes();
        // $schedule->command('youtube:comments')->everyFiveMinutes();
        $schedule->command('youtube:comments')->everyMinute();

        $schedule->command('emails:get')->everyFiveMinutes();
//        $schedule->job(new \App\Jobs\FetchEmailsJob())->everyFiveMinutes();

        $schedule->command('swap:expired')->dailyAt('00:00');
        $schedule->command('agent:satisfaction')->dailyAt('23:50');
        $schedule->command('agent:daily-rank')->dailyAt('23:55');
        $schedule->command('agent:monthly-rank')->monthly();
        $schedule->command('app:chatan-alytic')->dailyAt('06:00');
        $schedule->command('app:ziwo-refresh-token')->dailyAt('00:00');


    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
