<?php

namespace App\Exports\Reports\NonVoice;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;


class NonVoiceExport implements FromCollection, WithHeadings, WithMapping, ShouldAutoSize
{
    protected $records;
    protected $fields;
    protected $levels;

    public function __construct($records, $fields = [], $levels = [])
    {
        $this->records = $records;
        $this->fields = $fields;
        $this->levels  = $levels;
    }

    public function collection()
    {
        return collect($this->records);
    }

    public function map($row): array
    {
        $data = [
            $row['source_id'] ?? '-',                          // Source ID
            $row['channel'] ?? '-',                            // Channel
            $row['type'] ?? '-',                               // Type
            $row['direction'] ?? '-',                          // Direction
            $row['account'] ?? '-',                            // Account
            $row['customer_username'] ?? '-',                  // Customer Username
            $row['customer_name'] ?? '-',                      // Customer Name
            $row['status'] ?? '-',                             // Status
            $row['assigned_username'] ?? '-',                  // Assigned Username
            $row['assigned_name'] ?? '-',                      // Assigned Name
            $row['assigned_at'] ?? '-',                        // Assigned At
            $row['frt'] ?? '-',                                // FRT
            $row['SLA Response Time'] ?? '-',                  // SLA Response Time
            $row['SLA Handling Time'] ?? '-',                  // SLA Handling Time
        ];
        foreach ($this->levels as $level) {
            $levelKey = 'level_' . $level->id;
            $data[] = $row[$levelKey] ?? '-';
        }
        foreach ($this->fields as $field) {
            $data[] = $row[$field] ?? '-';
        }
        $data[] = $row['new_at'] ?? '-';                             // New At
        $data[] = $row['resolved_at'] ?? '-';                        // Resolved At
        $data[] = $row['closed_at'] ?? '-';                          // Closed At
        $data[] = $row['comment'] ?? '-';                            // Comment
        return $data;
    }

    public function headings(): array
    {
        $headings = [
            'Source ID',
            'Channel',
            'Type',
            'Direction',
            'Account',
            'Customer Username',
            'Customer Name',
            'Status',
            'Assigned Username',
            'Assigned Name',
            'Assigned At',
            'FRT',
            'SLA Response Time',
            'SLA Handling Time',
        ];
        foreach ($this->levels as $level) {
            $headings[] = $level->label;
        }
        foreach ($this->fields as $field) {
            // You might want to optimize this DB call if exporting many columns!
            $label = \App\Models\Tenant\FormField::where('slug', $field)->value('label') ?? $field;
            $headings[] = $label;
        }
        $headings[] = 'New At';
        $headings[] = 'Resolved At';
        $headings[] = 'Closed At';
        $headings[] = 'Comment';
        return $headings;
    }
}
