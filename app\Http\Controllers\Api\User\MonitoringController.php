<?php

namespace App\Http\Controllers\Api\User;

use App\Http\Controllers\Controller;
use App\Http\Resources\Supervisor\Monitoring\AvgTotalTimeResource;
use App\Http\Resources\Supervisor\Monitoring\dataResource;
use App\Http\Resources\Supervisor\Monitoring\StatuesResource;
use App\Http\Resources\Supervisor\Monitoring\TotalStatuesResource;
use App\Models\Tenant\AgentStatus;
use App\Models\Tenant\AgentStatusAction;
use App\Models\Tenant\Resource;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class MonitoringController extends Controller
{
    public function statues()
    {
        $statues = AgentStatus::all();
        return new StatuesResource($statues);
    }

    public function users(Request $request)
    {

        $statusId = isset($request->statusId) && $request->statusId != '' ? $request->statusId : null;

        $data = AgentStatusAction::with('User', 'AgentStatus');

        if ($statusId != null) {
            $data->where('agent_status_id', $statusId);
        }

        $data = $data->get();

        foreach ($data as $item) {
            // Access user ID for each AgentStatusAction
            $userId = $item->user_id;

            // Get count from OtherTable where user id matches
            $socialCount = Resource::where('user_id', $userId)->where('status','New')->whereHas('resourceInfo',function ($q)
            {
                $q->where(function ($q2){
                    $q2->where('channel','Facebook');
                    $q2->orWhere('channel','Instagram');
                    $q2->orWhere('channel','Google');
                });
            })->count();

            $emailCount = Resource::where('user_id', $userId)->where('status','New')->whereHas('resourceInfo',function ($q)
            {
                $q->where(function ($q2){

                    $q2->orWhere('channel','Email');
                });
            })->count();


            $chatCount = Resource::where('user_id', $userId)->where('status','New')->whereHas('resourceInfo',function ($q)
            {
                $q->where(function ($q2){
                    $q2->where('channel','Whatsapp');
                    $q2->orWhere('channel','Livechat');
                });
            })->count();

            $totalTimeDifference = Resource::where('user_id', $userId)
                ->where('status', 'New')
                ->whereDate('created_at', Carbon::today()) // Filter records where created_at is today
                ->get()
                ->sum(function ($resource) {
                    return $resource->updated_at->diffInSeconds($resource->created_at);
                });


            // Add the counter value to the item
            $item->social            = $socialCount;
            $item->chat              = $chatCount;
            $item->email             = $emailCount;
            $item->total_handle_time = $totalTimeDifference;
        }
        return dataResource::collection($data);
    }

    public function avg(Request $request)
    {
        $totalTimeDifference = AgentStatusAction::with('User', 'AgentStatus')
            ->get()
            ->map(function ($item) {
                $userId = $item->user_id;

                // Calculate the total time difference between created_at and updated_at
                $totalTimeDifference = Resource::where('user_id', $userId)
                    ->where('status', 'New')
                    ->whereDate('created_at', Carbon::today()) // Filter records where created_at is today
                    ->get()
                    ->sum(function ($resource) {
                        return $resource->updated_at->diffInSeconds($resource->created_at);
                    });

                // Add the total time difference to the item
                $item->total_time_difference = $totalTimeDifference;

                return $item;
            })
            ->pluck('total_time_difference') // Extract total_time_difference values
            ->avg(); // Calculate the average

        return new AvgTotalTimeResource(['avg_total_handle_time'=>$totalTimeDifference]);

    }

    public function counter(Request $request)
    {
        $statues = AgentStatus::all();

        $counts = [];
        foreach ($statues as $status) {
            $count = AgentStatusAction::where('agent_status_id', $status->id)->count();
            $counts[$status->name] = $count;
        }

        return new TotalStatuesResource($counts);

    }

}
