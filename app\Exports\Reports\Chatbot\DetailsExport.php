<?php

namespace App\Exports\Reports\Chatbot;

use Maatwebsite\Excel\Concerns\FromCollection;

class DetailsExport implements FromCollection
{
    /**
     * @return \Illuminate\Support\Collection
     */

    public $chatbots;

    public function __construct($chatbots)
    {
        $this->chatbots = $chatbots;

    }

    public function collection()
    {
        return $this->chatbots;
    }


    public function map($chatbot): array
    {
        return [
            $chatbot->id,
            $chatbot->botResourceInfo->channel,
            ucfirst($chatbot->botCustomer->display_name),
            $chatbot->status(),
            $chatbot->created_at
        ];
    }

    public function headings(): array
    {
        return [
            'Chat ID',
            'Channel',
            'Customer Name',
            'Status',
            'Created At',
        ];
    }
}
