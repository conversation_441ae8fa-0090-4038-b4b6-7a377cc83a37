<?php

namespace App\Console\Commands\Ranking;

use App\Models\Tenant;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class DailyTarget extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'agent:daily-rank';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'daily-rank';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $tenants = Tenant::with('domains')->get();

            foreach ($tenants as $tenant)
            {
                foreach ($tenant->domains as $domain)
                {
                    Http::get('https://'.$domain->domain.'/ranking/daily');
                }

            }
        }catch (\Exception $e)
        {

        }
    }
}
