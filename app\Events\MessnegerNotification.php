<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class MessnegerNotification implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */

    public $tenant_id;
    public $message;

    public function __construct($tenant_id, $message)
    {
        $this->tenant_id    =   $tenant_id;
        $this->message =   $message;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastWith()
    {
        return [
            'tenant_id'     =>$this->tenant_id,
            'content'       =>$this->message,

        ];
    }

    public function broadcastOn()
    {
        return new PrivateChannel('Messenger.'.$this->tenant_id);
    }
}
