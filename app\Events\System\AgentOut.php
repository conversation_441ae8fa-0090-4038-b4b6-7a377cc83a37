<?php

namespace App\Events\System;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class AgentOut implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $tenant_id;
    public $user_id;
    public $session;

    public function __construct($tenant_id, $user_id,$session)
    {
        $this->tenant_id    =   $tenant_id;
        $this->user_id      =   $user_id;
        $this->session      =   $session;
    }

    public function broadcastWith()
    {
        return [
            'tenant_id'     =>$this->tenant_id,
            'user_id'       =>$this->user_id,
            'session'       =>$this->session,

        ];
    }

    public function broadcastOn()
    {
        return new PrivateChannel('AgentOut.'.$this->tenant_id.'.'.$this->user_id);
    }
}
