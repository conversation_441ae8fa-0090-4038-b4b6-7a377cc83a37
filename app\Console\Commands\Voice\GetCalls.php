<?php

namespace App\Console\Commands\Voice;

use App\Models\Tenant;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class GetCalls extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'calls:get';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $tenants = Tenant::with('domains')->get();

            foreach ($tenants as $tenant)
            {
                foreach ($tenant->domains as $domain)
                {
                    Http::get('https://'.$domain->domain.'/voice/calls');
//                    Http::get('https://'.$domain->domain.'/escalation');
                }

            }
        }catch (\Exception $e)
        {

        }
    }
}
