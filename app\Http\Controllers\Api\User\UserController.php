<?php

namespace App\Http\Controllers\Api\User;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Role;
use App\Models\Tenant\User;
use Illuminate\Http\Request;

class UserController extends Controller
{
    public function logout(Request $request)
    {
        $request->user()->token()->revoke();
        return response()->json(['status' => 200, 'message' => 'Successfully logged out']);
    }

    public function info()
    {
        $user = \auth()->user();
        return response()->json(['status' => 200, 'message' => $user]);
    }

    public function role()
    {
        $role = User::where('id',\auth()->id())->with('roles')->first();
        return response()->json(['status' => 200, 'message' => $role->roles]);
    }

    public function permissions()
    {
        $role = User::where('id',\auth()->id())->with('roles', 'roles.permissions')->first();
        return response()->json(['status' => 200, 'message' => $role->roles[0]->permissions]);
    }

    public function search(Request $request)
    {
        // Validate the query parameter
        $request->validate([
            'search' => 'nullable|string|min:1',
        ]);

        // Get the search query
        $query = $request->input('search');

        // Fetch users matching the query
        $users = User::where('first_name', 'like', '%' . $query . '%')
            ->orWhere('last_name', 'like', '%' . $query . '%')
            ->orWhere('username', 'like', '%' . $query . '%')
            ->get(['id', 'first_name', 'last_name', 'username']); // Specify fields to return

        return response()->json([
            'users' => $users
        ]);
    }
}
