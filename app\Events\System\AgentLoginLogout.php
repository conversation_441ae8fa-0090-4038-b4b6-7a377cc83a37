<?php

namespace App\Events\System;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class AgentLoginLogout implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $tenant_id;
    public $user_id;

    public function __construct($tenant_id, $user_id)
    {
        $this->tenant_id    =   $tenant_id;
        $this->user_id      =   $user_id;
    }

    public function broadcastWith()
    {
        return [
            'tenant_id'     =>$this->tenant_id,
            'user_id'       =>$this->user_id,

        ];
    }

    public function broadcastOn()
    {
        return new PrivateChannel('AgentLogout.'.$this->tenant_id.'.'.$this->user_id);
    }
}
