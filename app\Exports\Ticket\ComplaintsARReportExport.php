<?php

namespace App\Exports\Ticket;

use App\Models\Tenant\Ticket;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Illuminate\Contracts\Queue\ShouldQueue;
use Maatwebsite\Excel\Concerns\Exportable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Bus\Queueable;
use Maatwebsite\Excel\Concerns\WithMapping;
use Illuminate\Support\Collection;

class ComplaintsARReportExport implements FromCollection, WithMapping, WithHeadings
{
    // use Exportable, Queueable, SerializesModels;

    protected $from;
    protected $to;

    public function __construct($from, $to)
    {
        $this->from = $from;
        $this->to = $to;
    }

    public function startCell(): string
    {
        return 'A1';
    }



    public function collection()
    {
        // Fetch the tickets with necessary relationships
        $tickets = Ticket::with(['ticketCustomer', 'ticketFiled', 'ticketInfo', 'ticketActions', 'ticketCategories.category'])
            ->whereDate('created_at', '>=', $this->from)
            ->whereDate('created_at', '<=', $this->to)
            ->get();

        // Filter tickets to only include complaints
        return $tickets->filter(function ($ticket) {
            $ticketCategory = $ticket->ticketCategories()->where('category_level_id', 1)->first();
            $categoryName = optional($ticketCategory->category)->name;

            return $categoryName === 'Complaints'; // Keep only complaints
        });
    }


    public function map($ticket): array
    {
        $fieldsArray = json_decode($ticket->ticketFiled->fields, true);
        $contactNumber = $fieldsArray['contact-number'] ?? $ticket->ticketCustomer->mobile_number ?? '-';

        $daysOpen = $ticket->status === 'Closed'
            ? $ticket->created_at->diffInDays($ticket->updated_at)
            : $ticket->created_at->diffInDays(now());

        $departmentId = optional($ticket->ticketActions()->where('department_id', 4)->first())->id ?? '';
        $departmentComment = $departmentId
            ? optional($ticket->ticketActions()->where('id', '>', $departmentId)->first())->comment ?? ''
            : '';

        $latestTicketAction = $ticket->ticketActions()->orderBy('created_at', 'desc')->first();
        $finalCustomerResponse = optional($latestTicketAction)->comment ?? '';

        $receivedFromEmployee = optional($latestTicketAction->user)->first_name . ' ' . optional($latestTicketAction->user)->last_name;

        return [
            'ID' => $ticket->id,
            'Customer Name' => $ticket->ticketCustomer->display_name ?? '',
            'Account Number' => '', // Account number placeholder
            'Contact Number' => $contactNumber,
            'Created At' => $ticket->created_at->format('Y/m/d H:i:s'),
            'Priority' => $ticket->ticketInfo->priority ?? '',
            'Product Type' => optional($ticket->ticketCategories()->where('category_level_id', 2)->first()->category)->name ?? '',
            'Category' => 'Complaints', // Default value since we filtered
            'Customer Comment' => $ticket->ticketInfo->comment ?? '',
            'Channel' => $ticket->ticketInfo->channel ?? '',
            'ID Card Number' => '', // ID card placeholder
            'Root Cause' => 'Complaints', // Default value since we filtered
            'Department Comment' => $departmentComment,
            'Final Customer Response' => $finalCustomerResponse,
            'Status Change Date' => $ticket->updated_at->format('Y/m/d H:i:s'),
            'Received From Employee' => $receivedFromEmployee,
            'Escalation' => 'Complaints', // Default value since we filtered
            'Days Open' => $daysOpen,
            'Expected Solution Days' => '10 Days',
            'Status' => $ticket->status ?? '',
        ];
    }


    public function headings(): array
    {
        return [
            // Your header rows here
            [
                'عناية العملاء', '', '', '', '', '', '', '', '', '', '', // 11 columns
                'الادارة المسؤولة', '', // 2 columns
                'إدارة قسم الشكاوى', '', '', '', '', '', '', '' // 7 columns
            ],
            [
                'رقم المعاملة', 'اسم العميل', 'رقم حساب العميل', 'رقم جوال العميل', 'تاريخ انشاء المعاملة',
                'تصنيف المعاملة', 'نوع المنتج', 'فئة المنتج', 'تفاصيل تذكرة العميل', 'القناة المستلمة للمعاملة',
                'رقم بطاقة الأحوال/ الإقامة', 'السبب الجذري للمعاملة', 'تعليقات القسم', 'الرد النهائي للعميل',
                'وقت وتاريخ تغيير الحالة', 'تم استلام المعاملة من الموظف', 'القسم', 'التصعيد', 'تاريخ الحل المتوقع للمعاملة',
                'حالة المعاملة'
            ]
        ];
    }
}
