<?php

namespace App\Http\Controllers\Tenant\Agent\Email;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Storage;

class EmailController extends Controller
{
    public function index(){
        return view('tenant.agent.emails.index');
    }

    public function image(Request $request)
    {
        if ($request->hasFile('upload')) {
            $originName = $request->file('upload')->getClientOriginalName();
            $originExtension = $request->file('upload')->getClientOriginalExtension();

            $filename = $originName . '_' . time() . '.' . $originExtension;

            Storage::disk('public')->put('email/' . $filename, $request->file('upload')->getContent());

            return response()->json([
                'fileName' => $filename,
                'uploaded' => 1,
                'url' => asset('email/' . $filename)
            ]);
        }
    }
}
